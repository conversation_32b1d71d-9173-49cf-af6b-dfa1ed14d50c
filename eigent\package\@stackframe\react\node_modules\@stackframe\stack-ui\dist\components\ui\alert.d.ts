import React from "react";
import { type VariantProps } from "class-variance-authority";
declare const Alert: React.FC<React.HTMLAttributes<HTMLDivElement> & VariantProps<(props?: ({
    variant?: "default" | "destructive" | "success" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const AlertTitle: React.FC<React.HTMLAttributes<HTMLHeadingElement> & {
    ref?: React.Ref<HTMLParagraphElement> | undefined;
}>;
declare const AlertDescription: React.FC<React.HTMLAttributes<HTMLParagraphElement> & {
    ref?: React.Ref<HTMLParagraphElement> | undefined;
}>;
export { Alert, AlertTitle, AlertDescription };
