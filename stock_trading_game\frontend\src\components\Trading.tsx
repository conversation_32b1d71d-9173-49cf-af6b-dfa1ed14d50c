import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  InputNumber, 
  Radio, 
  Typography,
  Space,
  Divider,
  Table,
  Tag,
  message
} from 'antd';
import { StockOutlined, DollarOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getStocks, createOrder, getUserOrders } from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface Stock {
  id: number;
  code: string;
  name: string;
  current_price: number;
  change_percent: number;
}

interface Order {
  id: number;
  order_no: string;
  stock_code: string;
  stock_name: string;
  order_type: string;
  price_type: string;
  price: number;
  quantity: number;
  status: string;
  created_at: string;
}

const Trading: React.FC = () => {
  const [form] = Form.useForm();
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [selectedStock, setSelectedStock] = useState<Stock | null>(null);
  const [orderType, setOrderType] = useState<'buy' | 'sell'>('buy');
  const [priceType, setPriceType] = useState<'market' | 'limit'>('market');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadStocks();
    loadOrders();
  }, []);

  const loadStocks = async () => {
    try {
      const data = await getStocks({ limit: 100 });
      setStocks(data);
    } catch (error) {
      console.error('加载股票数据失败:', error);
    }
  };

  const loadOrders = async () => {
    try {
      // 假设用户ID为1，实际应该从登录状态获取
      const data = await getUserOrders(1);
      setOrders(data);
    } catch (error) {
      console.error('加载订单数据失败:', error);
    }
  };

  const handleStockSelect = (stockId: number) => {
    const stock = stocks.find(s => s.id === stockId);
    setSelectedStock(stock || null);
    
    if (stock && priceType === 'limit') {
      form.setFieldsValue({ price: stock.current_price });
    }
  };

  const handleSubmit = async (values: any) => {
    if (!selectedStock) {
      message.error('请选择股票');
      return;
    }

    try {
      setLoading(true);
      
      const orderData = {
        user_id: 1, // 假设用户ID为1
        stock_id: selectedStock.id,
        order_type: orderType,
        price_type: priceType,
        quantity: values.quantity,
        price: priceType === 'limit' ? values.price : undefined,
      };

      await createOrder(orderData);
      message.success('订单提交成功！');
      
      // 重新加载订单列表
      loadOrders();
      
      // 重置表单
      form.resetFields();
      setSelectedStock(null);
      
    } catch (error: any) {
      message.error(error.response?.data?.detail || '订单提交失败');
    } finally {
      setLoading(false);
    }
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return '#ff4d4f';
    if (change < 0) return '#52c41a';
    return '#8c8c8c';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'processing';
      case 'filled': return 'success';
      case 'cancelled': return 'default';
      case 'partial_filled': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待成交';
      case 'filled': return '已成交';
      case 'cancelled': return '已取消';
      case 'partial_filled': return '部分成交';
      default: return status;
    }
  };

  const orderColumns: ColumnsType<Order> = [
    {
      title: '订单号',
      dataIndex: 'order_no',
      key: 'order_no',
      width: 120,
      render: (text: string) => <Text code>{text}</Text>,
    },
    {
      title: '股票',
      key: 'stock',
      width: 120,
      render: (_, record: Order) => (
        <div>
          <Text strong>{record.stock_code}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.stock_name}
          </Text>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'order_type',
      key: 'order_type',
      width: 60,
      render: (type: string) => (
        <Tag color={type === 'buy' ? 'red' : 'green'}>
          {type === 'buy' ? '买入' : '卖出'}
        </Tag>
      ),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      align: 'right',
      render: (price: number, record: Order) => (
        record.price_type === 'market' ? '市价' : `¥${price.toFixed(2)}`
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'right',
      render: (quantity: number) => quantity.toLocaleString(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time: string) => new Date(time).toLocaleString('zh-CN'),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        💰 交易中心
      </Title>

      <Row gutter={[16, 16]}>
        {/* 交易面板 */}
        <Col xs={24} lg={12}>
          <Card title="下单交易" extra={<StockOutlined />}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
            >
              {/* 买卖方向 */}
              <Form.Item label="交易方向">
                <Radio.Group 
                  value={orderType} 
                  onChange={(e) => setOrderType(e.target.value)}
                  buttonStyle="solid"
                >
                  <Radio.Button value="buy" style={{ color: '#ff4d4f' }}>
                    买入
                  </Radio.Button>
                  <Radio.Button value="sell" style={{ color: '#52c41a' }}>
                    卖出
                  </Radio.Button>
                </Radio.Group>
              </Form.Item>

              {/* 股票选择 */}
              <Form.Item 
                label="选择股票" 
                name="stock_id"
                rules={[{ required: true, message: '请选择股票' }]}
              >
                <Select
                  placeholder="搜索股票代码或名称"
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as string)
                      ?.toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  onChange={handleStockSelect}
                >
                  {stocks.map(stock => (
                    <Option key={stock.id} value={stock.id}>
                      <Space>
                        <Text strong>{stock.code}</Text>
                        <Text>{stock.name}</Text>
                        <Text style={{ color: getChangeColor(stock.change_percent) }}>
                          ¥{stock.current_price.toFixed(2)}
                        </Text>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 价格类型 */}
              <Form.Item label="价格类型">
                <Radio.Group 
                  value={priceType} 
                  onChange={(e) => setPriceType(e.target.value)}
                >
                  <Radio value="market">市价单</Radio>
                  <Radio value="limit">限价单</Radio>
                </Radio.Group>
              </Form.Item>

              {/* 价格输入 */}
              {priceType === 'limit' && (
                <Form.Item 
                  label="委托价格" 
                  name="price"
                  rules={[{ required: true, message: '请输入价格' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0.01}
                    step={0.01}
                    precision={2}
                    prefix="¥"
                  />
                </Form.Item>
              )}

              {/* 数量输入 */}
              <Form.Item 
                label="委托数量" 
                name="quantity"
                rules={[{ required: true, message: '请输入数量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={100}
                  step={100}
                  suffix="股"
                />
              </Form.Item>

              {/* 当前股票信息 */}
              {selectedStock && (
                <Card size="small" style={{ marginBottom: 16 }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text strong>
                      {selectedStock.code} - {selectedStock.name}
                    </Text>
                    <Text>
                      当前价格: 
                      <span style={{ 
                        color: getChangeColor(selectedStock.change_percent),
                        marginLeft: 8,
                        fontSize: 16,
                        fontWeight: 'bold'
                      }}>
                        ¥{selectedStock.current_price.toFixed(2)}
                      </span>
                    </Text>
                  </Space>
                </Card>
              )}

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  block
                  size="large"
                  style={{ 
                    backgroundColor: orderType === 'buy' ? '#ff4d4f' : '#52c41a',
                    borderColor: orderType === 'buy' ? '#ff4d4f' : '#52c41a'
                  }}
                >
                  {orderType === 'buy' ? '买入' : '卖出'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 订单列表 */}
        <Col xs={24} lg={12}>
          <Card title="我的订单" extra={<DollarOutlined />}>
            <Table
              columns={orderColumns}
              dataSource={orders}
              rowKey="id"
              size="small"
              pagination={{ pageSize: 10 }}
              scroll={{ x: 600 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Trading;
