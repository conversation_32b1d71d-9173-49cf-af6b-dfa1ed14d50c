import React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
declare const Tabs: React.ForwardRefExoticComponent<TabsPrimitive.TabsProps & React.RefAttributes<HTMLDivElement>>;
declare const TabsList: React.FC<Omit<TabsPrimitive.TabsListProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const TabsTrigger: React.FC<Omit<TabsPrimitive.TabsTriggerProps & React.RefAttributes<HTMLButtonElement>, "ref"> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
}>;
declare const TabsContent: React.FC<Omit<TabsPrimitive.TabsContentProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
export { Tabs, TabsList, TabsTrigger, TabsContent };
