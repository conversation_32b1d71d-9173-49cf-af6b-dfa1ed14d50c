{"version": 3, "sources": ["../../src/interface/webhooks.ts"], "sourcesContent": ["import * as yup from \"yup\";\nimport { teamMembershipCreatedWebhookEvent, teamMembershipDeletedWebhookEvent } from \"./crud/team-memberships\";\nimport { teamPermissionCreatedWebhookEvent, teamPermissionDeletedWebhookEvent } from \"./crud/team-permissions\";\nimport { teamCreatedWebhookEvent, teamDeletedWebhookEvent, teamUpdatedWebhookEvent } from \"./crud/teams\";\nimport { userCreatedWebhookEvent, userDeletedWebhookEvent, userUpdatedWebhookEvent } from \"./crud/users\";\n\nexport type WebhookEvent<S extends yup.Schema> = {\n  type: string,\n  schema: S,\n  metadata: {\n    summary: string,\n    description: string,\n    tags?: string[],\n  },\n};\n\nexport const webhookEvents = [\n  userCreatedWebhookEvent,\n  userUpdatedWebhookEvent,\n  userDeletedWebhookEvent,\n  teamCreatedWebhookEvent,\n  teamUpdatedWebhookEvent,\n  teamDeletedWebhookEvent,\n  teamMembershipCreatedWebhookEvent,\n  teamMembershipDeletedWebhookEvent,\n  teamPermissionCreatedWebhookEvent,\n  teamPermissionDeletedWebhookEvent,\n] as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,8BAAqF;AACrF,8BAAqF;AACrF,mBAA0F;AAC1F,mBAA0F;AAYnF,IAAM,gBAAgB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}