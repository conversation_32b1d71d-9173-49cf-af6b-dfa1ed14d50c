{"version": 3, "sources": ["../../src/utils/dom.tsx"], "sourcesContent": ["export function hasClickableParent(element: HTMLElement): boolean {\n  const parent = element.parentElement;\n  if (!parent) return false;\n  if (parent.dataset.n2Clickable) return true;\n\n  return hasClickableParent(element.parentElement);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,SAAS,mBAAmB,SAA+B;AAChE,QAAM,SAAS,QAAQ;AACvB,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,OAAO,QAAQ,YAAa,QAAO;AAEvC,SAAO,mBAAmB,QAAQ,aAAa;AACjD;", "names": []}