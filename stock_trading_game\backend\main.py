"""
🐾 模拟炒股游戏 - 主应用入口
Stock Trading Game - Main Application Entry Point

一个专业的模拟A股交易游戏后端服务
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger

from app.core.config import settings
from app.core.database import init_db
from app.core.redis_client import redis_client
from app.api.v1.api import api_router
from app.services.market_simulator import market_simulator


# 简化版本，移除暂时不需要的组件


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理"""
    logger.info("🚀 启动模拟炒股游戏服务...")
    
    # 初始化数据库
    await init_db()
    logger.info("✅ 数据库初始化完成")
    
    # 启动市场模拟器
    await market_simulator.start()
    logger.info("✅ 市场模拟器启动完成")
    
    logger.info("🎮 模拟炒股游戏服务启动成功！")
    
    yield
    
    # 关闭服务
    logger.info("🛑 正在关闭模拟炒股游戏服务...")
    await market_simulator.stop()
    await redis_client.close()
    logger.info("✅ 服务关闭完成")


# 创建FastAPI应用
app = FastAPI(
    title="模拟炒股游戏 API",
    description="一个专业的模拟A股交易游戏后端服务 🐾",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix="/api/v1")

# 静态文件服务
if settings.SERVE_STATIC:
    app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "🎮 模拟炒股游戏 API 服务正在运行",
        "version": "1.0.0",
        "status": "healthy",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "stock-trading-game",
        "version": "1.0.0"
    }


# WebSocket端点已在market.py中实现


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )
