{"version": 3, "sources": ["../../../src/interface/crud/email-templates.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { jsonSchema, yupBoolean, yupMixed, yupObject, yupString } from \"../../schema-fields\";\n\nexport type EmailTemplateType = typeof emailTemplateTypes[number];\nexport const emailTemplateTypes = ['email_verification', 'password_reset', 'magic_link', 'team_invitation'] as const;\n\nexport const emailTemplateAdminReadSchema = yupObject({\n  type: yupString().oneOf(emailTemplateTypes).defined(),\n  subject: yupString().defined(),\n  content: jsonSchema.defined(),\n  is_default: yupBoolean().defined(),\n}).defined();\n\nexport const emailTemplateCrudAdminUpdateSchema = yupObject({\n  content: jsonSchema.nonNullable().optional(),\n  subject: yupString().optional(),\n}).defined();\n\nexport const emailTemplateCrudAdminDeleteSchema = yupMixed();\n\nexport const emailTemplateCrudAdminCreateSchema = yupObject({\n  type: yupString().oneOf(emailTemplateTypes).defined(),\n  content: jsonSchema.defined(),\n  subject: yupString().defined(),\n}).defined();\n\nexport const emailTemplateCrud = createCrud({\n  adminReadSchema: emailTemplateAdminReadSchema,\n  adminUpdateSchema: emailTemplateCrudAdminUpdateSchema,\n  adminCreateSchema: emailTemplateCrudAdminCreateSchema,\n  adminDeleteSchema: emailTemplateCrudAdminDeleteSchema,\n  docs: {\n    adminRead: {\n      hidden: true,\n    },\n    adminCreate: {\n      hidden: true,\n    },\n    adminUpdate: {\n      hidden: true,\n    },\n    adminDelete: {\n      hidden: true,\n    },\n    adminList: {\n      hidden: true,\n    }\n  }\n});\nexport type EmailTemplateCrud = CrudTypeOf<typeof emailTemplateCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAAuE;AAGhE,IAAM,qBAAqB,CAAC,sBAAsB,kBAAkB,cAAc,iBAAiB;AAEnG,IAAM,mCAA+B,gCAAU;AAAA,EACpD,UAAM,gCAAU,EAAE,MAAM,kBAAkB,EAAE,QAAQ;AAAA,EACpD,aAAS,gCAAU,EAAE,QAAQ;AAAA,EAC7B,SAAS,gCAAW,QAAQ;AAAA,EAC5B,gBAAY,iCAAW,EAAE,QAAQ;AACnC,CAAC,EAAE,QAAQ;AAEJ,IAAM,yCAAqC,gCAAU;AAAA,EAC1D,SAAS,gCAAW,YAAY,EAAE,SAAS;AAAA,EAC3C,aAAS,gCAAU,EAAE,SAAS;AAChC,CAAC,EAAE,QAAQ;AAEJ,IAAM,yCAAqC,+BAAS;AAEpD,IAAM,yCAAqC,gCAAU;AAAA,EAC1D,UAAM,gCAAU,EAAE,MAAM,kBAAkB,EAAE,QAAQ;AAAA,EACpD,SAAS,gCAAW,QAAQ;AAAA,EAC5B,aAAS,gCAAU,EAAE,QAAQ;AAC/B,CAAC,EAAE,QAAQ;AAEJ,IAAM,wBAAoB,wBAAW;AAAA,EAC1C,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}