import React from "react";
import * as SwitchPrimitives from "@radix-ui/react-switch";
declare const Switch: React.FC<{
    onCheckedChange?: ((checked: boolean) => Promise<void> | void) | undefined;
    onClick?: ((e: React.MouseEvent<HTMLButtonElement>) => Promise<void> | void) | undefined;
    loading?: boolean | undefined;
} & SwitchPrimitives.SwitchProps & React.RefAttributes<HTMLButtonElement> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
}>;
export { Switch };
