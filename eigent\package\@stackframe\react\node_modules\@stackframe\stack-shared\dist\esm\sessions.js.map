{"version": 3, "sources": ["../../src/sessions.ts"], "sourcesContent": ["import * as jose from 'jose';\nimport { StackAssertionError } from \"./utils/errors\";\nimport { Store } from \"./utils/stores\";\n\nexport class AccessToken {\n  constructor(\n    public readonly token: string,\n  ) {\n    if (token === \"undefined\") {\n      throw new StackAssertionError(\"Access token is the string 'undefined'; it's unlikely this is the correct value. They're supposed to be unguessable!\");\n    }\n  }\n\n  get decoded() {\n    return jose.decodeJwt(this.token);\n  }\n\n  get expiresAt(): Date {\n    const { exp } = this.decoded;\n    if (exp === undefined) return new Date(8640000000000000);  // max date value\n    return new Date(exp * 1000);\n  }\n\n  /**\n   * @returns The number of milliseconds until the access token expires, or 0 if it has already expired.\n   */\n  get expiresInMillis(): number {\n    return Math.max(0, this.expiresAt.getTime() - Date.now());\n  }\n\n  isExpired(): boolean {\n    return this.expiresInMillis <= 0;\n  }\n}\n\nexport class RefreshToken {\n  constructor(\n    public readonly token: string,\n  ) {\n    if (token === \"undefined\") {\n      throw new StackAssertionError(\"Refresh token is the string 'undefined'; it's unlikely this is the correct value. They're supposed to be unguessable!\");\n    }\n  }\n}\n\n/**\n * An InternalSession represents a user's session, which may or may not be valid. It may contain an access token, a refresh token, or both.\n *\n * A session never changes which user or session it belongs to, but the tokens in it may change over time.\n */\nexport class InternalSession {\n  /**\n  * Each session has a session key that depends on the tokens inside. If the session has a refresh token, the session key depends only on the refresh token. If the session does not have a refresh token, the session key depends only on the access token.\n  *\n  * Multiple Session objects may have the same session key, which implies that they represent the same session by the same user. Furthermore, a session's key never changes over the lifetime of a session object.\n  *\n  * This is useful for caching and indexing sessions.\n  */\n  public readonly sessionKey: string;\n\n  /**\n   * An access token that is not known to be invalid (ie. may be valid, but may have expired).\n   */\n  private _accessToken: Store<AccessToken | null>;\n  private readonly _refreshToken: RefreshToken | null;\n\n  /**\n   * Whether the session as a whole is known to be invalid (ie. both access and refresh tokens are invalid). Used as a cache to avoid making multiple requests to the server (sessions never go back to being valid after being invalidated).\n   *\n   * It is possible for the access token to be invalid but the refresh token to be valid, in which case the session is\n   * still valid (just needs a refresh). It is also possible for the access token to be valid but the refresh token to\n   * be invalid, in which case the session is also valid (eg. if the refresh token is null because the user only passed\n   * in an access token, eg. in a server-side request handler).\n   */\n  private _knownToBeInvalid = new Store<boolean>(false);\n\n  private _refreshPromise: Promise<AccessToken | null> | null = null;\n\n  constructor(private readonly _options: {\n    refreshAccessTokenCallback(refreshToken: RefreshToken): Promise<AccessToken | null>,\n    refreshToken: string | null,\n    accessToken?: string | null,\n  }) {\n    this._accessToken = new Store(_options.accessToken ? new AccessToken(_options.accessToken) : null);\n    this._refreshToken = _options.refreshToken ? new RefreshToken(_options.refreshToken) : null;\n    if (_options.accessToken === null && _options.refreshToken === null) {\n      // this session is already invalid\n      this._knownToBeInvalid.set(true);\n    }\n    this.sessionKey = InternalSession.calculateSessionKey({ accessToken: _options.accessToken ?? null, refreshToken: _options.refreshToken });\n  }\n\n  static calculateSessionKey(ofTokens: { refreshToken: string | null, accessToken?: string | null }): string {\n    if (ofTokens.refreshToken) {\n      return `refresh-${ofTokens.refreshToken}`;\n    } else if (ofTokens.accessToken) {\n      return `access-${ofTokens.accessToken}`;\n    } else {\n      return \"not-logged-in\";\n    }\n  }\n\n  isKnownToBeInvalid() {\n    return this._knownToBeInvalid.get();\n  }\n\n  /**\n   * Marks the session object as invalid, meaning that the refresh and access tokens can no longer be used.\n   */\n  markInvalid() {\n    this._accessToken.set(null);\n    this._knownToBeInvalid.set(true);\n  }\n\n  onInvalidate(callback: () => void): { unsubscribe: () => void } {\n    return this._knownToBeInvalid.onChange(() => callback());\n  }\n\n  /**\n   * Returns the access token if it is found in the cache, fetching it otherwise.\n   *\n   * This is usually the function you want to call to get an access token. Either set `minMillisUntilExpiration` to a reasonable value, or catch errors that occur if it expires, and call `markAccessTokenExpired` to mark the token as expired if so (after which a call to this function will always refetch the token).\n   *\n   * @returns null if the session is known to be invalid, cached tokens if they exist in the cache (which may or may not be valid still), or new tokens otherwise.\n   */\n  async getOrFetchLikelyValidTokens(minMillisUntilExpiration: number): Promise<{ accessToken: AccessToken, refreshToken: RefreshToken | null } | null> {\n    if (minMillisUntilExpiration >= 60_000) {\n      throw new Error(`Required access token expiry ${minMillisUntilExpiration}ms is too long; access tokens are too short to be used for more than 60s`);\n    }\n\n    const accessToken = this._getPotentiallyInvalidAccessTokenIfAvailable();\n    if (!accessToken || accessToken.expiresInMillis < minMillisUntilExpiration) {\n      const newTokens = await this.fetchNewTokens();\n      const expiresInMillis = newTokens?.accessToken.expiresInMillis;\n      if (expiresInMillis && expiresInMillis < minMillisUntilExpiration) {\n        throw new StackAssertionError(`Required access token expiry ${minMillisUntilExpiration}ms is too long; access tokens are too short when they're generated (${expiresInMillis}ms)`);\n      }\n      return newTokens;\n    }\n    return { accessToken, refreshToken: this._refreshToken };\n  }\n\n  /**\n   * Fetches new tokens that are, at the time of fetching, guaranteed to be valid.\n   *\n   * The newly generated tokens are short-lived, so it's good practice not to rely on their validity (if possible). However, this function is useful in some cases where you only want to pass access tokens to a service, and you want to make sure said access token has the longest possible lifetime.\n   *\n   * In most cases, you should prefer `getOrFetchLikelyValidTokens`.\n   *\n   * @returns null if the session is known to be invalid, or new tokens otherwise (which, at the time of fetching, are guaranteed to be valid).\n   */\n  async fetchNewTokens(): Promise<{ accessToken: AccessToken, refreshToken: RefreshToken | null } | null> {\n    const accessToken = await this._getNewlyFetchedAccessToken();\n    return accessToken ? { accessToken, refreshToken: this._refreshToken } : null;\n  }\n\n  markAccessTokenExpired(accessToken: AccessToken) {\n    // TODO we don't need this anymore, since we now check the expiry by ourselves\n    if (this._accessToken.get() === accessToken) {\n      this._accessToken.set(null);\n    }\n  }\n\n  /**\n   * Note that a callback invocation with `null` does not mean the session has been invalidated; the access token may just have expired. Use `onInvalidate` to detect invalidation.\n   */\n  onAccessTokenChange(callback: (newAccessToken: AccessToken | null) => void): { unsubscribe: () => void } {\n    return this._accessToken.onChange(callback);\n  }\n\n  /**\n   * @returns An access token, which may be expired or expire soon, or null if it is known to be invalid.\n   */\n  private _getPotentiallyInvalidAccessTokenIfAvailable(): AccessToken | null {\n    if (!this._refreshToken) return null;\n    if (this.isKnownToBeInvalid()) return null;\n\n    const accessToken = this._accessToken.get();\n    if (accessToken && !accessToken.isExpired()) return accessToken;\n\n    return null;\n  }\n\n  /**\n   * You should prefer `_getOrFetchPotentiallyInvalidAccessToken` in almost all cases.\n   *\n   * @returns A newly fetched access token (never read from cache), or null if the session either does not represent a user or the session is invalid.\n   */\n  private async _getNewlyFetchedAccessToken(): Promise<AccessToken | null> {\n    if (!this._refreshToken) return null;\n    if (this._knownToBeInvalid.get()) return null;\n\n    if (!this._refreshPromise) {\n      this._refreshAndSetRefreshPromise(this._refreshToken);\n    }\n    return await this._refreshPromise;\n  }\n\n  private _refreshAndSetRefreshPromise(refreshToken: RefreshToken) {\n    let refreshPromise: Promise<AccessToken | null> = this._options.refreshAccessTokenCallback(refreshToken).then((accessToken) => {\n      if (refreshPromise === this._refreshPromise) {\n        this._refreshPromise = null;\n        this._accessToken.set(accessToken);\n        if (!accessToken) {\n          this.markInvalid();\n        }\n      }\n      return accessToken;\n    });\n    this._refreshPromise = refreshPromise;\n  }\n}\n"], "mappings": ";AAAA,YAAY,UAAU;AACtB,SAAS,2BAA2B;AACpC,SAAS,aAAa;AAEf,IAAM,cAAN,MAAkB;AAAA,EACvB,YACkB,OAChB;AADgB;AAEhB,QAAI,UAAU,aAAa;AACzB,YAAM,IAAI,oBAAoB,sHAAsH;AAAA,IACtJ;AAAA,EACF;AAAA,EAEA,IAAI,UAAU;AACZ,WAAY,eAAU,KAAK,KAAK;AAAA,EAClC;AAAA,EAEA,IAAI,YAAkB;AACpB,UAAM,EAAE,IAAI,IAAI,KAAK;AACrB,QAAI,QAAQ,OAAW,QAAO,oBAAI,KAAK,MAAgB;AACvD,WAAO,IAAI,KAAK,MAAM,GAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,kBAA0B;AAC5B,WAAO,KAAK,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,EAC1D;AAAA,EAEA,YAAqB;AACnB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AACF;AAEO,IAAM,eAAN,MAAmB;AAAA,EACxB,YACkB,OAChB;AADgB;AAEhB,QAAI,UAAU,aAAa;AACzB,YAAM,IAAI,oBAAoB,uHAAuH;AAAA,IACvJ;AAAA,EACF;AACF;AAOO,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EA4B3B,YAA6B,UAI1B;AAJ0B;AAJ7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAQ,oBAAoB,IAAI,MAAe,KAAK;AAEpD,SAAQ,kBAAsD;AAO5D,SAAK,eAAe,IAAI,MAAM,SAAS,cAAc,IAAI,YAAY,SAAS,WAAW,IAAI,IAAI;AACjG,SAAK,gBAAgB,SAAS,eAAe,IAAI,aAAa,SAAS,YAAY,IAAI;AACvF,QAAI,SAAS,gBAAgB,QAAQ,SAAS,iBAAiB,MAAM;AAEnE,WAAK,kBAAkB,IAAI,IAAI;AAAA,IACjC;AACA,SAAK,aAAa,iBAAgB,oBAAoB,EAAE,aAAa,SAAS,eAAe,MAAM,cAAc,SAAS,aAAa,CAAC;AAAA,EAC1I;AAAA,EAEA,OAAO,oBAAoB,UAAgF;AACzG,QAAI,SAAS,cAAc;AACzB,aAAO,WAAW,SAAS,YAAY;AAAA,IACzC,WAAW,SAAS,aAAa;AAC/B,aAAO,UAAU,SAAS,WAAW;AAAA,IACvC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,kBAAkB,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,kBAAkB,IAAI,IAAI;AAAA,EACjC;AAAA,EAEA,aAAa,UAAmD;AAC9D,WAAO,KAAK,kBAAkB,SAAS,MAAM,SAAS,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,4BAA4B,0BAAmH;AACnJ,QAAI,4BAA4B,KAAQ;AACtC,YAAM,IAAI,MAAM,gCAAgC,wBAAwB,0EAA0E;AAAA,IACpJ;AAEA,UAAM,cAAc,KAAK,6CAA6C;AACtE,QAAI,CAAC,eAAe,YAAY,kBAAkB,0BAA0B;AAC1E,YAAM,YAAY,MAAM,KAAK,eAAe;AAC5C,YAAM,kBAAkB,WAAW,YAAY;AAC/C,UAAI,mBAAmB,kBAAkB,0BAA0B;AACjE,cAAM,IAAI,oBAAoB,gCAAgC,wBAAwB,uEAAuE,eAAe,KAAK;AAAA,MACnL;AACA,aAAO;AAAA,IACT;AACA,WAAO,EAAE,aAAa,cAAc,KAAK,cAAc;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAkG;AACtG,UAAM,cAAc,MAAM,KAAK,4BAA4B;AAC3D,WAAO,cAAc,EAAE,aAAa,cAAc,KAAK,cAAc,IAAI;AAAA,EAC3E;AAAA,EAEA,uBAAuB,aAA0B;AAE/C,QAAI,KAAK,aAAa,IAAI,MAAM,aAAa;AAC3C,WAAK,aAAa,IAAI,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAqF;AACvG,WAAO,KAAK,aAAa,SAAS,QAAQ;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKQ,+CAAmE;AACzE,QAAI,CAAC,KAAK,cAAe,QAAO;AAChC,QAAI,KAAK,mBAAmB,EAAG,QAAO;AAEtC,UAAM,cAAc,KAAK,aAAa,IAAI;AAC1C,QAAI,eAAe,CAAC,YAAY,UAAU,EAAG,QAAO;AAEpD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAc,8BAA2D;AACvE,QAAI,CAAC,KAAK,cAAe,QAAO;AAChC,QAAI,KAAK,kBAAkB,IAAI,EAAG,QAAO;AAEzC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,6BAA6B,KAAK,aAAa;AAAA,IACtD;AACA,WAAO,MAAM,KAAK;AAAA,EACpB;AAAA,EAEQ,6BAA6B,cAA4B;AAC/D,QAAI,iBAA8C,KAAK,SAAS,2BAA2B,YAAY,EAAE,KAAK,CAAC,gBAAgB;AAC7H,UAAI,mBAAmB,KAAK,iBAAiB;AAC3C,aAAK,kBAAkB;AACvB,aAAK,aAAa,IAAI,WAAW;AACjC,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACD,SAAK,kBAAkB;AAAA,EACzB;AACF;", "names": []}