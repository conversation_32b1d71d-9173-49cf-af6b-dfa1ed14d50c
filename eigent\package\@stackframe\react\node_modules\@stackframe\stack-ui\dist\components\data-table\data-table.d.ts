import { ColumnDef, ColumnFiltersState, SortingState, Table as TableType, VisibilityState } from "@tanstack/react-table";
import React from "react";
export declare function TableView<TData, TValue>(props: {
    table: TableType<TData>;
    columns: ColumnDef<TData, TValue>[];
    toolbarRender?: (table: TableType<TData>) => React.ReactNode;
    showDefaultToolbar?: boolean;
    defaultColumnFilters: ColumnFiltersState;
    defaultSorting: SortingState;
    onRowClick?: (row: TData) => void;
}): import("react/jsx-runtime").JSX.Element;
type DataTableProps<TData, TValue> = {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    toolbarRender?: (table: TableType<TData>) => React.ReactNode;
    defaultVisibility?: VisibilityState;
    defaultColumnFilters: ColumnFiltersState;
    defaultSorting: SortingState;
    showDefaultToolbar?: boolean;
    onRowClick?: (row: TData) => void;
};
export declare function DataTable<TData, TValue>({ columns, data, toolbarRender, defaultVisibility, defaultColumnFilters, defaultSorting, showDefaultToolbar, }: DataTableProps<TData, TValue>): import("react/jsx-runtime").JSX.Element;
type DataTableManualPaginationProps<TData, TValue> = DataTableProps<TData, TValue> & {
    onUpdate: (options: {
        cursor: string;
        limit: number;
        sorting: SortingState;
        columnFilters: ColumnFiltersState;
        globalFilters: any;
    }) => Promise<{
        nextCursor: string | null;
    }>;
};
export declare function DataTableManualPagination<TData, TValue>({ columns, data, toolbarRender, defaultVisibility, defaultColumnFilters, defaultSorting, onRowClick, onUpdate, showDefaultToolbar, }: DataTableManualPaginationProps<TData, TValue>): import("react/jsx-runtime").JSX.Element;
export {};
