import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, List, Typography, Tag, Space, Spin } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, FireOutlined } from '@ant-design/icons';
import { getMarketOverview, getHotStocks, getGainers, getLosers } from '../services/api';

const { Title, Text } = Typography;

interface MarketOverview {
  total_stocks: number;
  rising_stocks: number;
  falling_stocks: number;
  unchanged_stocks: number;
  total_volume: number;
  total_turnover: number;
  market_sentiment: number;
  is_market_open: boolean;
}

interface Stock {
  id: number;
  code: string;
  name: string;
  current_price: number;
  change_percent: number;
  change_amount: number;
  volume: number;
  turnover?: number;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [marketData, setMarketData] = useState<MarketOverview | null>(null);
  const [hotStocks, setHotStocks] = useState<Stock[]>([]);
  const [gainers, setGainers] = useState<Stock[]>([]);
  const [losers, setLosers] = useState<Stock[]>([]);

  useEffect(() => {
    loadDashboardData();
    
    // 每30秒刷新一次数据
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [overview, hot, topGainers, topLosers] = await Promise.all([
        getMarketOverview(),
        getHotStocks(5),
        getGainers(5),
        getLosers(5)
      ]);
      
      setMarketData(overview);
      setHotStocks(hot);
      setGainers(topGainers);
      setLosers(topLosers);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 100000000) {
      return `${(num / 100000000).toFixed(2)}亿`;
    } else if (num >= 10000) {
      return `${(num / 10000).toFixed(2)}万`;
    }
    return num.toLocaleString();
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return '#ff4d4f';
    if (change < 0) return '#52c41a';
    return '#8c8c8c';
  };

  const renderStockItem = (stock: Stock) => (
    <List.Item key={stock.id}>
      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
        <div>
          <Text strong>{stock.code}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>{stock.name}</Text>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div style={{ color: getChangeColor(stock.change_percent) }}>
            ¥{stock.current_price.toFixed(2)}
          </div>
          <div style={{ 
            color: getChangeColor(stock.change_percent),
            fontSize: 12
          }}>
            {stock.change_percent > 0 ? '+' : ''}{stock.change_percent.toFixed(2)}%
          </div>
        </div>
      </Space>
    </List.Item>
  );

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        🐾 市场概览
        <Tag 
          color={marketData?.is_market_open ? 'green' : 'red'} 
          style={{ marginLeft: 16 }}
        >
          {marketData?.is_market_open ? '开市中' : '休市中'}
        </Tag>
      </Title>

      {/* 市场统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总股票数"
              value={marketData?.total_stocks || 0}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="上涨股票"
              value={marketData?.rising_stocks || 0}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ArrowUpOutlined />}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="下跌股票"
              value={marketData?.falling_stocks || 0}
              valueStyle={{ color: '#52c41a' }}
              prefix={<ArrowDownOutlined />}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总成交额"
              value={formatNumber(marketData?.total_turnover || 0)}
              prefix="¥"
            />
          </Card>
        </Col>
      </Row>

      {/* 股票排行 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card 
            title={
              <Space>
                <FireOutlined style={{ color: '#ff7a45' }} />
                热门股票
              </Space>
            }
            size="small"
          >
            <List
              size="small"
              dataSource={hotStocks}
              renderItem={renderStockItem}
            />
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card 
            title={
              <Space>
                <ArrowUpOutlined style={{ color: '#ff4d4f' }} />
                涨幅榜
              </Space>
            }
            size="small"
          >
            <List
              size="small"
              dataSource={gainers}
              renderItem={renderStockItem}
            />
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card 
            title={
              <Space>
                <ArrowDownOutlined style={{ color: '#52c41a' }} />
                跌幅榜
              </Space>
            }
            size="small"
          >
            <List
              size="small"
              dataSource={losers}
              renderItem={renderStockItem}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
