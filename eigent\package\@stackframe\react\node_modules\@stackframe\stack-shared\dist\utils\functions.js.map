{"version": 3, "sources": ["../../src/utils/functions.tsx"], "sourcesContent": ["export function identity<T>(t: T): T {\n  return t;\n}\nundefined?.test(\"identity\", ({ expect }) => {\n  expect(identity(1)).toBe(1);\n  expect(identity(\"test\")).toBe(\"test\");\n  expect(identity(null)).toBe(null);\n  expect(identity(undefined)).toBe(undefined);\n  const obj = { a: 1 };\n  expect(identity(obj)).toBe(obj);\n});\n\nexport function identityArgs<T extends any[]>(...args: T): T {\n  return args;\n}\nundefined?.test(\"identityArgs\", ({ expect }) => {\n  expect(identityArgs()).toEqual([]);\n  expect(identityArgs(1)).toEqual([1]);\n  expect(identityArgs(1, 2, 3)).toEqual([1, 2, 3]);\n  expect(identityArgs(\"a\", \"b\", \"c\")).toEqual([\"a\", \"b\", \"c\"]);\n  expect(identityArgs(null, undefined)).toEqual([null, undefined]);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,SAAS,SAAY,GAAS;AACnC,SAAO;AACT;AAUO,SAAS,gBAAiC,MAAY;AAC3D,SAAO;AACT;", "names": []}