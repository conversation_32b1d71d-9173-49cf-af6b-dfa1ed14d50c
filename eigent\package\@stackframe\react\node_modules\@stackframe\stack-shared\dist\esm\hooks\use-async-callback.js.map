{"version": 3, "sources": ["../../../src/hooks/use-async-callback.tsx"], "sourcesContent": ["import React from \"react\";\nimport { captureError } from \"../utils/errors\";\n\nexport function useAsyncCallback<A extends any[], R>(\n  callback: (...args: A) => Promise<R>,\n  deps: React.DependencyList\n): [cb: (...args: A) => Promise<R>, loading: boolean, error: unknown | undefined] {\n  const [error, setError] = React.useState<unknown | undefined>(undefined);\n  const [loadingCount, setLoadingCount] = React.useState(0);\n\n  const cb = React.useCallback(\n    async (...args: A) => {\n      setLoadingCount((c) => c + 1);\n      try {\n        return await callback(...args);\n      } catch (e) {\n        setError(e);\n        throw e;\n      } finally {\n        setLoadingCount((c) => c - 1);\n      }\n    },\n    deps,\n  );\n\n  return [cb, loadingCount > 0, error];\n}\n\nexport function useAsyncCallbackWithLoggedError<A extends any[], R>(\n  callback: (...args: A) => Promise<R>,\n  deps: React.DependencyList\n): [cb: (...args: A) => Promise<R>, loading: boolean] {\n  const [newCallback, loading] = useAsyncCallback<A, R>(async (...args) => {\n    try {\n      return await callback(...args);\n    } catch (e) {\n      captureError(\"async-callback\", e);\n      throw e;\n    }\n  }, deps);\n\n  return [newCallback, loading];\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,SAAS,oBAAoB;AAEtB,SAAS,iBACd,UACA,MACgF;AAChF,QAAM,CAAC,OAAO,QAAQ,IAAI,MAAM,SAA8B,MAAS;AACvE,QAAM,CAAC,cAAc,eAAe,IAAI,MAAM,SAAS,CAAC;AAExD,QAAM,KAAK,MAAM;AAAA,IACf,UAAU,SAAY;AACpB,sBAAgB,CAAC,MAAM,IAAI,CAAC;AAC5B,UAAI;AACF,eAAO,MAAM,SAAS,GAAG,IAAI;AAAA,MAC/B,SAAS,GAAG;AACV,iBAAS,CAAC;AACV,cAAM;AAAA,MACR,UAAE;AACA,wBAAgB,CAAC,MAAM,IAAI,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA;AAAA,EACF;AAEA,SAAO,CAAC,IAAI,eAAe,GAAG,KAAK;AACrC;AAEO,SAAS,gCACd,UACA,MACoD;AACpD,QAAM,CAAC,aAAa,OAAO,IAAI,iBAAuB,UAAU,SAAS;AACvE,QAAI;AACF,aAAO,MAAM,SAAS,GAAG,IAAI;AAAA,IAC/B,SAAS,GAAG;AACV,mBAAa,kBAAkB,CAAC;AAChC,YAAM;AAAA,IACR;AAAA,EACF,GAAG,IAAI;AAEP,SAAO,CAAC,aAAa,OAAO;AAC9B;", "names": []}