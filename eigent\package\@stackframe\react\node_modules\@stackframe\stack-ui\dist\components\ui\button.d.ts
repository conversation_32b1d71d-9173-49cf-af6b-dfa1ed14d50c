import { type VariantProps } from "class-variance-authority";
import React from "react";
declare const buttonVariants: (props?: ({
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "plain" | null | undefined;
    size?: "default" | "plain" | "sm" | "lg" | "icon" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string;
export type OriginalButtonProps = {
    asChild?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof buttonVariants>;
type ButtonProps = {
    onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>;
    loading?: boolean;
} & OriginalButtonProps;
declare const Button: React.FC<{
    onClick?: ((e: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>) | undefined;
    loading?: boolean | undefined;
} & {
    asChild?: boolean | undefined;
} & React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<(props?: ({
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "plain" | null | undefined;
    size?: "default" | "plain" | "sm" | "lg" | "icon" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
}>;
export { Button, ButtonProps, buttonVariants };
