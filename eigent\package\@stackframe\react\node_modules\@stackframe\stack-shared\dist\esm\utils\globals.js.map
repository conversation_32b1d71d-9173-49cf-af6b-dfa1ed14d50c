{"version": 3, "sources": ["../../../src/utils/globals.tsx"], "sourcesContent": ["const globalVar: any =\n  typeof globalThis !== 'undefined' ? globalThis :\n    typeof global !== 'undefined' ? global :\n      typeof window !== 'undefined' ? window :\n        typeof self !== 'undefined' ? self :\n          {};\nexport {\n  globalVar,\n};\n\nif (typeof globalThis === 'undefined') {\n  (globalVar as any).globalThis = globalVar;\n}\n\nconst stackGlobalsSymbol = Symbol.for('__stack-globals');\nglobalVar[stackGlobalsSymbol] ??= {};\n\nexport function createGlobal<T>(key: string, init: () => T) {\n  if (!globalVar[stackGlobalsSymbol][key]) {\n    globalVar[stackGlobalsSymbol][key] = init();\n  }\n  return globalVar[stackGlobalsSymbol][key] as T;\n}\n"], "mappings": ";AAAA,IAAM,YACJ,OAAO,eAAe,cAAc,aAClC,OAAO,WAAW,cAAc,SAC9B,OAAO,WAAW,cAAc,SAC9B,OAAO,SAAS,cAAc,OAC5B,CAAC;AAKX,IAAI,OAAO,eAAe,aAAa;AACrC,EAAC,UAAkB,aAAa;AAClC;AAEA,IAAM,qBAAqB,OAAO,IAAI,iBAAiB;AACvD,UAAU,kBAAkB,MAAM,CAAC;AAE5B,SAAS,aAAgB,KAAa,MAAe;AAC1D,MAAI,CAAC,UAAU,kBAAkB,EAAE,GAAG,GAAG;AACvC,cAAU,kBAAkB,EAAE,GAAG,IAAI,KAAK;AAAA,EAC5C;AACA,SAAO,UAAU,kBAAkB,EAAE,GAAG;AAC1C;", "names": []}