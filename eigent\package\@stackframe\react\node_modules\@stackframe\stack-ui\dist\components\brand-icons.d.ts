export declare function Google({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Facebook({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function GitHub({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Microsoft({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Spotify({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Discord({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Gitlab({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Bitbucket({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function LinkedIn({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Apple({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function X({ iconSize }: {
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function Mapping({ provider, iconSize, }: {
    provider: string;
    iconSize: number;
}): import("react/jsx-runtime").JSX.Element;
export declare function toTitle(id: string): string;
export declare const BRAND_COLORS: Record<string, string>;
