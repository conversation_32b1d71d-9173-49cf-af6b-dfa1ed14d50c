#!/usr/bin/env python3
"""
🐾 开发环境启动脚本
Development Environment Startup Script

快速启动模拟炒股游戏开发环境
"""

import asyncio
import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "backend"))

def print_banner():
    """打印启动横幅"""
    banner = """
    🐾 模拟炒股游戏开发环境
    ========================
    Stock Trading Game Dev Environment
    
    正在启动开发服务...
    """
    print(banner)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 11):
        print("❌ 需要Python 3.11或更高版本")
        return False
    
    # 检查必要的包
    required_packages = [
        "fastapi", "uvicorn", "sqlalchemy", "redis", "asyncpg"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r backend/requirements.txt")
        return False
    
    print("✅ 依赖检查通过")
    return True

def setup_environment():
    """设置环境变量"""
    print("⚙️ 设置环境变量...")
    
    # 设置开发环境
    os.environ["ENVIRONMENT"] = "development"
    os.environ["DEBUG"] = "true"
    
    # 数据库配置（使用SQLite进行快速开发）
    db_path = project_root / "backend" / "dev.db"
    os.environ["DATABASE_URL"] = f"sqlite+aiosqlite:///{db_path}"
    
    # Redis配置（如果没有Redis，使用内存模拟）
    os.environ["REDIS_URL"] = "redis://localhost:6379/0"
    
    print("✅ 环境变量设置完成")

async def init_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    
    try:
        from app.core.database import init_db
        await init_db()
        print("✅ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

async def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    try:
        # 切换到backend目录
        backend_dir = project_root / "backend"
        os.chdir(backend_dir)
        
        # 启动uvicorn服务器
        import uvicorn
        config = uvicorn.Config(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")

def start_frontend():
    """启动前端服务（如果存在）"""
    frontend_dir = project_root / "frontend"
    if frontend_dir.exists() and (frontend_dir / "package.json").exists():
        print("🎨 启动前端服务...")
        try:
            subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                shell=True
            )
            print("✅ 前端服务启动完成 (http://localhost:3000)")
        except Exception as e:
            print(f"⚠️ 前端服务启动失败: {e}")
    else:
        print("ℹ️ 前端项目不存在，跳过前端启动")

async def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 设置环境
    setup_environment()
    
    # 初始化数据库
    if not await init_database():
        return
    
    # 启动前端（非阻塞）
    start_frontend()
    
    # 启动后端（阻塞）
    print("🎮 模拟炒股游戏开发环境启动完成！")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("🎨 前端界面: http://localhost:3000 (如果已启动)")
    print("\n按 Ctrl+C 停止服务")
    
    await start_backend()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
