import React from "react";
declare const Breadcrumb: React.FC<Omit<React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>, "ref"> & {
    separator?: React.ReactNode;
} & {
    ref?: React.Ref<HTMLElement> | undefined;
}>;
declare const BreadcrumbList: React.FC<Omit<React.DetailedHTMLProps<React.OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, "ref"> & {
    ref?: React.Ref<HTMLOListElement> | undefined;
}>;
declare const BreadcrumbItem: React.FC<Omit<React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, "ref"> & {
    ref?: React.Ref<HTMLLIElement> | undefined;
}>;
declare const BreadcrumbLink: React.FC<Omit<React.DetailedHTMLProps<React.AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, "ref"> & {
    asChild?: boolean | undefined;
} & {
    ref?: React.Ref<HTMLAnchorElement> | undefined;
}>;
declare const BreadcrumbPage: React.FC<Omit<React.DetailedHTMLProps<React.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, "ref"> & {
    ref?: React.Ref<HTMLSpanElement> | undefined;
}>;
declare const BreadcrumbSeparator: {
    ({ children, className, ...props }: React.ComponentProps<"li">): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
declare const BreadcrumbEllipsis: {
    ({ className, ...props }: React.ComponentProps<"span">): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
export { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator, BreadcrumbEllipsis, };
