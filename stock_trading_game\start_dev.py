#!/usr/bin/env python3
"""
🐾 开发环境启动脚本
Development Environment Startup Script

同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import threading
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    🐾 模拟炒股游戏开发环境启动器
    ================================
    
    正在启动前后端服务...
    """
    print(banner)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    # 检查Python依赖
    backend_requirements = Path("backend/requirements.txt")
    if backend_requirements.exists():
        print("✅ 后端依赖文件存在")
    else:
        print("❌ 后端依赖文件不存在")
        return False
    
    # 检查Node.js依赖
    frontend_package = Path("frontend/package.json")
    if frontend_package.exists():
        print("✅ 前端依赖文件存在")
    else:
        print("❌ 前端依赖文件不存在")
        return False
    
    return True

def install_backend_deps():
    """安装后端依赖"""
    print("📦 安装后端依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True, cwd=os.getcwd())
        print("✅ 后端依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 后端依赖安装失败: {e}")
        return False

def install_frontend_deps():
    """安装前端依赖"""
    print("📦 安装前端依赖...")
    try:
        subprocess.run([
            "npm", "install"
        ], check=True, cwd="frontend")
        print("✅ 前端依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 前端依赖安装失败: {e}")
        return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    try:
        # 切换到后端目录
        os.chdir("backend")
        
        # 启动FastAPI服务
        subprocess.run([
            sys.executable, "-m", "uvicorn", "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n🛑 后端服务已停止")
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    try:
        # 等待后端启动
        time.sleep(3)
        
        # 启动React服务
        subprocess.run([
            "npm", "start"
        ], cwd="frontend")
    except KeyboardInterrupt:
        print("\n🛑 前端服务已停止")
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请检查项目结构")
        return
    
    # 询问是否安装依赖
    install_deps = input("是否需要安装依赖？(y/N): ").lower().strip()
    if install_deps in ['y', 'yes']:
        print("📦 开始安装依赖...")
        
        # 安装后端依赖
        if not install_backend_deps():
            return
        
        # 安装前端依赖
        if not install_frontend_deps():
            return
        
        print("✅ 所有依赖安装完成")
    
    print("\n🎮 启动开发服务器...")
    print("后端地址: http://localhost:8000")
    print("前端地址: http://localhost:3000")
    print("API文档: http://localhost:8000/docs")
    print("\n按 Ctrl+C 停止服务")
    
    try:
        # 创建线程启动后端
        backend_thread = threading.Thread(target=start_backend, daemon=True)
        backend_thread.start()
        
        # 启动前端（主线程）
        start_frontend()
        
    except KeyboardInterrupt:
        print("\n👋 开发服务器已停止")

if __name__ == "__main__":
    main()
