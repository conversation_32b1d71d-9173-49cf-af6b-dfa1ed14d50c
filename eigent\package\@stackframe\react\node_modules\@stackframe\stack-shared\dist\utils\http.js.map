{"version": 3, "sources": ["../../src/utils/http.tsx"], "sourcesContent": ["import { decodeBase64, encodeBase64, isBase64 } from \"./bytes\";\n\nexport const HTTP_METHODS = {\n  \"GET\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"POST\": {\n    safe: false,\n    idempotent: false,\n  },\n  \"PUT\": {\n    safe: false,\n    idempotent: true,\n  },\n  \"DELETE\": {\n    safe: false,\n    idempotent: true,\n  },\n  \"PATCH\": {\n    safe: false,\n    idempotent: false,\n  },\n  \"OPTIONS\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"HEAD\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"TRACE\": {\n    safe: true,\n    idempotent: true,\n  },\n  \"CONNECT\": {\n    safe: false,\n    idempotent: false,\n  },\n} as const;\nexport type HttpMethod = keyof typeof HTTP_METHODS;\n\nexport function decodeBasicAuthorizationHeader(value: string): [string, string] | null {\n  const [type, encoded, ...rest] = value.split(' ');\n  if (rest.length > 0) return null;\n  if (!encoded) return null;\n  if (type !== 'Basic') return null;\n  if (!isBase64(encoded)) return null;\n  const decoded = new TextDecoder().decode(decodeBase64(encoded));\n  const split = decoded.split(':');\n  return [split[0], split.slice(1).join(':')];\n}\nundefined?.test(\"decodeBasicAuthorizationHeader\", ({ expect }) => {\n  // Test with valid Basic Authorization header\n  const username = \"user\";\n  const password = \"pass\";\n  const encoded = encodeBasicAuthorizationHeader(username, password);\n  expect(decodeBasicAuthorizationHeader(encoded)).toEqual([username, password]);\n\n  // Test with password containing colons\n  const complexPassword = \"pass:with:colons\";\n  const encodedComplex = encodeBasicAuthorizationHeader(username, complexPassword);\n  expect(decodeBasicAuthorizationHeader(encodedComplex)).toEqual([username, complexPassword]);\n\n  // Test with invalid headers\n  expect(decodeBasicAuthorizationHeader(\"NotBasic dXNlcjpwYXNz\")).toBe(null); // Wrong type\n  expect(decodeBasicAuthorizationHeader(\"Basic\")).toBe(null); // Missing encoded part\n  expect(decodeBasicAuthorizationHeader(\"Basic not-base64\")).toBe(null); // Not base64\n  expect(decodeBasicAuthorizationHeader(\"Basic dXNlcjpwYXNz extra\")).toBe(null); // Extra parts\n});\n\nexport function encodeBasicAuthorizationHeader(id: string, password: string): string {\n  if (id.includes(':')) throw new Error(\"Basic authorization header id cannot contain ':'\");\n  return `Basic ${encodeBase64(new TextEncoder().encode(`${id}:${password}`))}`;\n}\nundefined?.test(\"encodeBasicAuthorizationHeader\", ({ expect }) => {\n  // Test with simple username and password\n  const encoded = encodeBasicAuthorizationHeader(\"user\", \"pass\");\n  expect(encoded).toMatch(/^Basic [A-Za-z0-9+/=]+$/); // Should start with \"Basic \" followed by base64\n\n  // Test with empty password\n  const encodedEmptyPass = encodeBasicAuthorizationHeader(\"user\", \"\");\n  expect(encodedEmptyPass).toMatch(/^Basic [A-Za-z0-9+/=]+$/);\n\n  // Test with password containing special characters\n  const encodedSpecialChars = encodeBasicAuthorizationHeader(\"user\", \"p@ss!w0rd\");\n  expect(encodedSpecialChars).toMatch(/^Basic [A-Za-z0-9+/=]+$/);\n\n  // Test with username containing colon should throw\n  expect(() => encodeBasicAuthorizationHeader(\"user:name\", \"pass\")).toThrow();\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAqD;AAE9C,IAAM,eAAe;AAAA,EAC1B,OAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,YAAY;AAAA,EACd;AACF;AAGO,SAAS,+BAA+B,OAAwC;AACrF,QAAM,CAAC,MAAM,SAAS,GAAG,IAAI,IAAI,MAAM,MAAM,GAAG;AAChD,MAAI,KAAK,SAAS,EAAG,QAAO;AAC5B,MAAI,CAAC,QAAS,QAAO;AACrB,MAAI,SAAS,QAAS,QAAO;AAC7B,MAAI,KAAC,uBAAS,OAAO,EAAG,QAAO;AAC/B,QAAM,UAAU,IAAI,YAAY,EAAE,WAAO,2BAAa,OAAO,CAAC;AAC9D,QAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,SAAO,CAAC,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC;AAC5C;AAoBO,SAAS,+BAA+B,IAAY,UAA0B;AACnF,MAAI,GAAG,SAAS,GAAG,EAAG,OAAM,IAAI,MAAM,kDAAkD;AACxF,SAAO,aAAS,2BAAa,IAAI,YAAY,EAAE,OAAO,GAAG,EAAE,IAAI,QAAQ,EAAE,CAAC,CAAC;AAC7E;", "names": []}