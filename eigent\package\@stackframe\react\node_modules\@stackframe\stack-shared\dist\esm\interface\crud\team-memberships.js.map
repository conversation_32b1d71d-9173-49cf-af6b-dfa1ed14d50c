{"version": 3, "sources": ["../../../../src/interface/crud/team-memberships.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupMixed, yupObject, yupString } from \"../../schema-fields\";\nimport { WebhookEvent } from \"../webhooks\";\n\nexport const teamMembershipsCrudClientReadSchema = yupObject({\n  team_id: yupString().defined(),\n  user_id: yupString().defined(),\n}).defined();\nexport const teamMembershipsCrudServerCreateSchema = yupObject({}).defined();\nexport const teamMembershipsCrudClientDeleteSchema = yupMixed();\n\nexport const teamMembershipsCrud = createCrud({\n  // Client\n  clientReadSchema: teamMembershipsCrudClientReadSchema,\n  clientDeleteSchema: teamMembershipsCrudClientDeleteSchema,\n  // Server\n  serverCreateSchema: teamMembershipsCrudServerCreateSchema,\n  docs: {\n    serverCreate: {\n      summary: \"Add a user to a team\",\n      description: \"\",\n      tags: [\"Teams\"],\n    },\n    clientDelete: {\n      summary: \"Remove a user from a team\",\n      description: \"All the users are allowed to remove themselves from a team (`user_id=me`). Only the users who have the `$remove_members` permission are allowed to remove other users from a team. `team_id` is must an ID of a team that the user is a member of.\",\n      tags: [\"Teams\"],\n    },\n    serverDelete: {\n      summary: \"Remove a user from a team\",\n      description: \"\",\n      tags: [\"Teams\"],\n    },\n  },\n});\nexport type TeamMembershipsCrud = CrudTypeOf<typeof teamMembershipsCrud>;\n\nexport const teamMembershipCreatedWebhookEvent = {\n  type: \"team_membership.created\",\n  schema: teamMembershipsCrud.server.readSchema,\n  metadata: {\n    summary: \"Team Membership Created\",\n    description: \"This event is triggered when a user is added to a team.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof teamMembershipsCrud.server.readSchema>;\n\nexport const teamMembershipDeletedWebhookEvent = {\n  type: \"team_membership.deleted\",\n  schema: teamMembershipsCrud.server.readSchema,\n  metadata: {\n    summary: \"Team Membership Deleted\",\n    description: \"This event is triggered when a user is removed from a team.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof teamMembershipsCrud.server.readSchema>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,SAAS,UAAU,WAAW,iBAAiB;AAGxC,IAAM,sCAAsC,UAAU;AAAA,EAC3D,SAAS,UAAU,EAAE,QAAQ;AAAA,EAC7B,SAAS,UAAU,EAAE,QAAQ;AAC/B,CAAC,EAAE,QAAQ;AACJ,IAAM,wCAAwC,UAAU,CAAC,CAAC,EAAE,QAAQ;AACpE,IAAM,wCAAwC,SAAS;AAEvD,IAAM,sBAAsB,WAAW;AAAA;AAAA,EAE5C,kBAAkB;AAAA,EAClB,oBAAoB;AAAA;AAAA,EAEpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,OAAO;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AAGM,IAAM,oCAAoC;AAAA,EAC/C,MAAM;AAAA,EACN,QAAQ,oBAAoB,OAAO;AAAA,EACnC,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEO,IAAM,oCAAoC;AAAA,EAC/C,MAAM;AAAA,EACN,QAAQ,oBAAoB,OAAO;AAAA,EACnC,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;", "names": []}