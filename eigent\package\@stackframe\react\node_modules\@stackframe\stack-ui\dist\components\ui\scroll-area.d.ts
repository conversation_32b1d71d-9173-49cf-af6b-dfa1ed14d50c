import React from "react";
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area";
declare const ScrollArea: React.FC<Omit<ScrollAreaPrimitive.ScrollAreaProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const ScrollBar: React.FC<Omit<ScrollAreaPrimitive.ScrollAreaScrollbarProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
export { ScrollArea, ScrollBar };
