import React from "react";
export declare function TextCell(props: {
    children: React.ReactNode;
    size?: number;
    icon?: React.ReactNode;
}): import("react/jsx-runtime").JSX.Element;
export declare function AvatarCell(props: {
    src?: string;
    fallback?: string;
}): import("react/jsx-runtime").JSX.Element;
export declare function DateCell(props: {
    date: Date;
    ignoreAfterYears?: number;
}): import("react/jsx-runtime").JSX.Element;
type ActionItem = '-' | {
    item: React.ReactNode;
    onClick: (e: React.MouseEvent) => void | Promise<void>;
    danger?: boolean;
    disabled?: boolean;
    disabledTooltip?: string;
};
export declare function ActionCell(props: {
    items?: ActionItem[];
    invisible?: boolean;
}): import("react/jsx-runtime").JSX.Element;
export declare function BadgeCell(props: {
    badges: string[];
    size?: number;
}): import("react/jsx-runtime").JSX.Element;
export {};
