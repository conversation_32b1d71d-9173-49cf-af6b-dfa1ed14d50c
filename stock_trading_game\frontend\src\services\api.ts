/**
 * 🐾 API服务层
 * API Service Layer
 * 
 * 封装所有与后端API的交互
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

// 通用请求函数
async function request<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`API请求失败: ${endpoint}`, error);
    throw error;
  }
}

// GET请求
async function get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
  const searchParams = params ? new URLSearchParams(params).toString() : '';
  const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
  
  return request<T>(url, { method: 'GET' });
}

// POST请求
async function post<T>(endpoint: string, data?: any): Promise<T> {
  return request<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

// PUT请求
async function put<T>(endpoint: string, data?: any): Promise<T> {
  return request<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
}

// DELETE请求
async function del<T>(endpoint: string): Promise<T> {
  return request<T>(endpoint, { method: 'DELETE' });
}

// ==================== 股票相关API ====================

export interface Stock {
  id: number;
  code: string;
  name: string;
  industry: string;
  current_price: number;
  opening_price: number;
  previous_close: number;
  change_amount: number;
  change_percent: number;
  highest_price: number;
  lowest_price: number;
  volume: number;
  turnover: number;
  limit_up: number;
  limit_down: number;
  is_active: boolean;
}

export interface StockParams {
  skip?: number;
  limit?: number;
  search?: string;
  industry?: string;
}

// 获取股票列表
export const getStocks = (params?: StockParams): Promise<Stock[]> => {
  return get<Stock[]>('/stocks/', params);
};

// 获取单只股票详情
export const getStock = (stockId: number): Promise<Stock> => {
  return get<Stock>(`/stocks/${stockId}`);
};

// 获取股票实时数据
export const getStockRealtime = (stockId: number): Promise<any> => {
  return get<any>(`/stocks/${stockId}/realtime`);
};

// ==================== 市场相关API ====================

export interface MarketOverview {
  total_stocks: number;
  rising_stocks: number;
  falling_stocks: number;
  unchanged_stocks: number;
  total_volume: number;
  total_turnover: number;
  market_sentiment: number;
  is_market_open: boolean;
}

// 获取市场概览
export const getMarketOverview = (): Promise<MarketOverview> => {
  return get<MarketOverview>('/market/overview');
};

// 获取热门股票
export const getHotStocks = (limit: number = 10): Promise<Stock[]> => {
  return get<Stock[]>('/market/hot-stocks', { limit });
};

// 获取涨幅榜
export const getGainers = (limit: number = 10): Promise<Stock[]> => {
  return get<Stock[]>('/market/gainers', { limit });
};

// 获取跌幅榜
export const getLosers = (limit: number = 10): Promise<Stock[]> => {
  return get<Stock[]>('/market/losers', { limit });
};

// ==================== 用户相关API ====================

export interface User {
  id: number;
  username: string;
  nickname: string;
  email: string;
  balance: number;
  total_assets: number;
  level: number;
  created_at: string;
}

export interface Portfolio {
  user_id: number;
  total_assets: number;
  available_balance: number;
  frozen_balance: number;
  total_market_value: number;
  total_profit: number;
  positions: Position[];
}

export interface Position {
  stock_id: number;
  stock_code: string;
  stock_name: string;
  quantity: number;
  available_quantity: number;
  avg_cost: number;
  current_price: number;
  market_value: number;
  profit_loss: number;
  profit_loss_rate: number;
  weight: number;
}

// 获取用户详情
export const getUser = (userId: number): Promise<User> => {
  return get<User>(`/users/${userId}`);
};

// 获取用户投资组合
export const getUserPortfolio = (userId: number): Promise<Portfolio> => {
  return get<Portfolio>(`/users/${userId}/portfolio`);
};

// 创建用户
export const createUser = (userData: {
  username: string;
  password: string;
  email: string;
  nickname?: string;
}): Promise<User> => {
  return post<User>('/users/', userData);
};

// ==================== 订单相关API ====================

export interface Order {
  id: number;
  order_no: string;
  user_id: number;
  stock_id: number;
  stock_code?: string;
  stock_name?: string;
  order_type: 'buy' | 'sell';
  price_type: 'market' | 'limit';
  price?: number;
  quantity: number;
  filled_quantity: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CreateOrderData {
  user_id: number;
  stock_id: number;
  order_type: 'buy' | 'sell';
  price_type: 'market' | 'limit';
  quantity: number;
  price?: number;
}

// 获取订单列表
export const getOrders = (params?: {
  user_id?: number;
  status?: string;
  skip?: number;
  limit?: number;
}): Promise<Order[]> => {
  return get<Order[]>('/orders/', params);
};

// 获取用户订单
export const getUserOrders = (userId: number): Promise<Order[]> => {
  return get<Order[]>(`/orders/user/${userId}/active`);
};

// 创建订单
export const createOrder = (orderData: CreateOrderData): Promise<Order> => {
  return post<Order>('/orders/', orderData);
};

// 取消订单
export const cancelOrder = (orderId: number, reason?: string): Promise<Order> => {
  return post<Order>(`/orders/${orderId}/cancel`, { reason });
};

// ==================== WebSocket相关 ====================

export class MarketWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private onMessage?: (data: any) => void;
  private onError?: (error: Event) => void;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(onMessage?: (data: any) => void, onError?: (error: Event) => void) {
    this.url = `ws://localhost:8000/api/v1/market/ws`;
    this.onMessage = onMessage;
    this.onError = onError;
  }

  connect() {
    try {
      this.ws = new WebSocket(this.url);
      
      this.ws.onopen = () => {
        console.log('🔌 WebSocket连接已建立');
        this.reconnectAttempts = 0;
      };
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.onMessage?.(data);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('🔌 WebSocket连接已关闭');
        this.attemptReconnect();
      };
      
      this.ws.onerror = (error) => {
        console.error('❌ WebSocket错误:', error);
        this.onError?.(error);
      };
      
    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, 3000 * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }
}

export default {
  // 股票相关
  getStocks,
  getStock,
  getStockRealtime,
  
  // 市场相关
  getMarketOverview,
  getHotStocks,
  getGainers,
  getLosers,
  
  // 用户相关
  getUser,
  getUserPortfolio,
  createUser,
  
  // 订单相关
  getOrders,
  getUserOrders,
  createOrder,
  cancelOrder,
  
  // WebSocket
  MarketWebSocket,
};
