import React from "react";
import * as AvatarPrimitive from "@radix-ui/react-avatar";
declare const Avatar: React.FC<Omit<AvatarPrimitive.AvatarProps & React.RefAttributes<HTMLSpanElement>, "ref"> & {
    ref?: React.Ref<HTMLSpanElement> | undefined;
}>;
declare const AvatarImage: React.FC<Omit<AvatarPrimitive.AvatarImageProps & React.RefAttributes<HTMLImageElement>, "ref"> & {
    ref?: React.Ref<HTMLImageElement> | undefined;
}>;
declare const AvatarFallback: React.FC<Omit<AvatarPrimitive.AvatarFallbackProps & React.RefAttributes<HTMLSpanElement>, "ref"> & {
    ref?: React.Ref<HTMLSpanElement> | undefined;
}>;
export { Avatar, AvatarImage, AvatarFallback };
