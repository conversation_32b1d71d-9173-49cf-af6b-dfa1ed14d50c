{"version": 3, "sources": ["../../../src/utils/stores.tsx"], "sourcesContent": ["import { ReadWriteLock } from \"./locks\";\nimport { ReactPromise, pending, rejected, resolved } from \"./promises\";\nimport { AsyncResult, Result } from \"./results\";\nimport { generateUuid } from \"./uuids\";\n\nexport type ReadonlyStore<T> = {\n  get(): T,\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n};\n\nexport type AsyncStoreStateChangeCallback<T> = (args: { state: AsyncResult<T>, oldState: AsyncResult<T>, lastOkValue: T | undefined }) => void;\n\nexport type ReadonlyAsyncStore<T> = {\n  isAvailable(): boolean,\n  get(): AsyncResult<T, unknown, void>,\n  getOrWait(): ReactPromise<T>,\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void },\n  onStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void },\n  onceStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void },\n};\n\nexport class Store<T> implements ReadonlyStore<T> {\n  private readonly _callbacks: Map<string, ((value: T, oldValue: T | undefined) => void)> = new Map();\n\n  constructor(\n    private _value: T\n  ) {}\n\n  get(): T {\n    return this._value;\n  }\n\n  set(value: T): void {\n    const oldValue = this._value;\n    this._value = value;\n    this._callbacks.forEach((callback) => callback(value, oldValue));\n  }\n\n  update(updater: (value: T) => T): T {\n    const value = updater(this._value);\n    this.set(value);\n    return value;\n  }\n\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const uuid = generateUuid();\n    this._callbacks.set(uuid, callback);\n    return {\n      unsubscribe: () => {\n        this._callbacks.delete(uuid);\n      },\n    };\n  }\n\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const { unsubscribe } = this.onChange((...args) => {\n      unsubscribe();\n      callback(...args);\n    });\n    return { unsubscribe };\n  }\n}\n\nexport const storeLock = new ReadWriteLock();\n\n\nexport class AsyncStore<T> implements ReadonlyAsyncStore<T> {\n  private _isAvailable: boolean;\n  private _mostRecentOkValue: T | undefined = undefined;\n\n  private _isRejected = false;\n  private _rejectionError: unknown;\n  private readonly _waitingRejectFunctions = new Map<string, ((error: unknown) => void)>();\n\n  private readonly _callbacks: Map<string, AsyncStoreStateChangeCallback<T>> = new Map();\n\n  private _updateCounter = 0;\n  private _lastSuccessfulUpdate = -1;\n\n  constructor(...args: [] | [T]) {\n    if (args.length === 0) {\n      this._isAvailable = false;\n    } else {\n      this._isAvailable = true;\n      this._mostRecentOkValue = args[0];\n    }\n  }\n\n  isAvailable(): boolean {\n    return this._isAvailable;\n  }\n\n  isRejected(): boolean {\n    return this._isRejected;\n  }\n\n  get() {\n    if (this.isRejected()) {\n      return AsyncResult.error(this._rejectionError);\n    } else if (this.isAvailable()) {\n      return AsyncResult.ok(this._mostRecentOkValue as T);\n    } else {\n      return AsyncResult.pending();\n    }\n  }\n\n  getOrWait(): ReactPromise<T> {\n    const uuid = generateUuid();\n    if (this.isRejected()) {\n      return rejected(this._rejectionError);\n    } else if (this.isAvailable()) {\n      return resolved(this._mostRecentOkValue as T);\n    }\n    const promise = new Promise<T>((resolve, reject) => {\n      this.onceChange((value) => {\n        resolve(value);\n      });\n      this._waitingRejectFunctions.set(uuid, reject);\n    });\n    const withFinally = promise.finally(() => {\n      this._waitingRejectFunctions.delete(uuid);\n    });\n    return pending(withFinally);\n  }\n\n  _setIfLatest(result: Result<T>, curCounter: number) {\n    const oldState = this.get();\n    const oldValue = this._mostRecentOkValue;\n    if (curCounter > this._lastSuccessfulUpdate) {\n      switch (result.status) {\n        case \"ok\": {\n          if (!this._isAvailable || this._isRejected || this._mostRecentOkValue !== result.data) {\n            this._lastSuccessfulUpdate = curCounter;\n            this._isAvailable = true;\n            this._isRejected = false;\n            this._mostRecentOkValue = result.data;\n            this._rejectionError = undefined;\n            this._callbacks.forEach((callback) => callback({\n              state: this.get(),\n              oldState,\n              lastOkValue: oldValue,\n            }));\n            return true;\n          }\n          return false;\n        }\n        case \"error\": {\n          this._lastSuccessfulUpdate = curCounter;\n          this._isAvailable = false;\n          this._isRejected = true;\n          this._rejectionError = result.error;\n          this._waitingRejectFunctions.forEach((reject) => reject(result.error));\n          this._callbacks.forEach((callback) => callback({\n            state: this.get(),\n            oldState,\n            lastOkValue: oldValue,\n          }));\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  set(value: T): void {\n    this._setIfLatest(Result.ok(value), ++this._updateCounter);\n  }\n\n  update(updater: (value: T | undefined) => T): T {\n    const value = updater(this._mostRecentOkValue);\n    this.set(value);\n    return value;\n  }\n\n  async setAsync(promise: Promise<T>): Promise<boolean> {\n    return await storeLock.withReadLock(async () => {\n      const curCounter = ++this._updateCounter;\n      const result = await Result.fromPromise(promise);\n      return this._setIfLatest(result, curCounter);\n    });\n  }\n\n  setUnavailable(): void {\n    this._lastSuccessfulUpdate = ++this._updateCounter;\n    this._isAvailable = false;\n    this._isRejected = false;\n    this._rejectionError = undefined;\n  }\n\n  setRejected(error: unknown): void {\n    this._setIfLatest(Result.error(error), ++this._updateCounter);\n  }\n\n  map<U>(mapper: (value: T) => U): AsyncStore<U> {\n    const store = new AsyncStore<U>();\n    this.onChange((value) => {\n      store.set(mapper(value));\n    });\n    return store;\n  }\n\n  onChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    return this.onStateChange(({ state, lastOkValue }) => {\n      if (state.status === \"ok\") {\n        callback(state.data, lastOkValue);\n      }\n    });\n  }\n\n  onStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void } {\n    const uuid = generateUuid();\n    this._callbacks.set(uuid, callback);\n    return {\n      unsubscribe: () => {\n        this._callbacks.delete(uuid);\n      },\n    };\n  }\n\n  onceChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const { unsubscribe } = this.onChange((...args) => {\n      unsubscribe();\n      callback(...args);\n    });\n    return { unsubscribe };\n  }\n\n  onceStateChange(callback: AsyncStoreStateChangeCallback<T>): { unsubscribe: () => void } {\n    const { unsubscribe } = this.onStateChange((...args) => {\n      unsubscribe();\n      callback(...args);\n    });\n    return { unsubscribe };\n  }\n}\n"], "mappings": ";AAAA,SAAS,qBAAqB;AAC9B,SAAuB,SAAS,UAAU,gBAAgB;AAC1D,SAAS,aAAa,cAAc;AACpC,SAAS,oBAAoB;AAoBtB,IAAM,QAAN,MAA2C;AAAA,EAGhD,YACU,QACR;AADQ;AAHV,SAAiB,aAAyE,oBAAI,IAAI;AAAA,EAI/F;AAAA,EAEH,MAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAgB;AAClB,UAAM,WAAW,KAAK;AACtB,SAAK,SAAS;AACd,SAAK,WAAW,QAAQ,CAAC,aAAa,SAAS,OAAO,QAAQ,CAAC;AAAA,EACjE;AAAA,EAEA,OAAO,SAA6B;AAClC,UAAM,QAAQ,QAAQ,KAAK,MAAM;AACjC,SAAK,IAAI,KAAK;AACd,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,UAAoF;AAC3F,UAAM,OAAO,aAAa;AAC1B,SAAK,WAAW,IAAI,MAAM,QAAQ;AAClC,WAAO;AAAA,MACL,aAAa,MAAM;AACjB,aAAK,WAAW,OAAO,IAAI;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,UAAoF;AAC7F,UAAM,EAAE,YAAY,IAAI,KAAK,SAAS,IAAI,SAAS;AACjD,kBAAY;AACZ,eAAS,GAAG,IAAI;AAAA,IAClB,CAAC;AACD,WAAO,EAAE,YAAY;AAAA,EACvB;AACF;AAEO,IAAM,YAAY,IAAI,cAAc;AAGpC,IAAM,aAAN,MAAM,YAA+C;AAAA,EAa1D,eAAe,MAAgB;AAX/B,SAAQ,qBAAoC;AAE5C,SAAQ,cAAc;AAEtB,SAAiB,0BAA0B,oBAAI,IAAwC;AAEvF,SAAiB,aAA4D,oBAAI,IAAI;AAErF,SAAQ,iBAAiB;AACzB,SAAQ,wBAAwB;AAG9B,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,eAAe;AACpB,WAAK,qBAAqB,KAAK,CAAC;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,cAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,aAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM;AACJ,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO,YAAY,MAAM,KAAK,eAAe;AAAA,IAC/C,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAO,YAAY,GAAG,KAAK,kBAAuB;AAAA,IACpD,OAAO;AACL,aAAO,YAAY,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,YAA6B;AAC3B,UAAM,OAAO,aAAa;AAC1B,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO,SAAS,KAAK,eAAe;AAAA,IACtC,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAO,SAAS,KAAK,kBAAuB;AAAA,IAC9C;AACA,UAAM,UAAU,IAAI,QAAW,CAAC,SAAS,WAAW;AAClD,WAAK,WAAW,CAAC,UAAU;AACzB,gBAAQ,KAAK;AAAA,MACf,CAAC;AACD,WAAK,wBAAwB,IAAI,MAAM,MAAM;AAAA,IAC/C,CAAC;AACD,UAAM,cAAc,QAAQ,QAAQ,MAAM;AACxC,WAAK,wBAAwB,OAAO,IAAI;AAAA,IAC1C,CAAC;AACD,WAAO,QAAQ,WAAW;AAAA,EAC5B;AAAA,EAEA,aAAa,QAAmB,YAAoB;AAClD,UAAM,WAAW,KAAK,IAAI;AAC1B,UAAM,WAAW,KAAK;AACtB,QAAI,aAAa,KAAK,uBAAuB;AAC3C,cAAQ,OAAO,QAAQ;AAAA,QACrB,KAAK,MAAM;AACT,cAAI,CAAC,KAAK,gBAAgB,KAAK,eAAe,KAAK,uBAAuB,OAAO,MAAM;AACrF,iBAAK,wBAAwB;AAC7B,iBAAK,eAAe;AACpB,iBAAK,cAAc;AACnB,iBAAK,qBAAqB,OAAO;AACjC,iBAAK,kBAAkB;AACvB,iBAAK,WAAW,QAAQ,CAAC,aAAa,SAAS;AAAA,cAC7C,OAAO,KAAK,IAAI;AAAA,cAChB;AAAA,cACA,aAAa;AAAA,YACf,CAAC,CAAC;AACF,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,SAAS;AACZ,eAAK,wBAAwB;AAC7B,eAAK,eAAe;AACpB,eAAK,cAAc;AACnB,eAAK,kBAAkB,OAAO;AAC9B,eAAK,wBAAwB,QAAQ,CAAC,WAAW,OAAO,OAAO,KAAK,CAAC;AACrE,eAAK,WAAW,QAAQ,CAAC,aAAa,SAAS;AAAA,YAC7C,OAAO,KAAK,IAAI;AAAA,YAChB;AAAA,YACA,aAAa;AAAA,UACf,CAAC,CAAC;AACF,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,OAAgB;AAClB,SAAK,aAAa,OAAO,GAAG,KAAK,GAAG,EAAE,KAAK,cAAc;AAAA,EAC3D;AAAA,EAEA,OAAO,SAAyC;AAC9C,UAAM,QAAQ,QAAQ,KAAK,kBAAkB;AAC7C,SAAK,IAAI,KAAK;AACd,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAS,SAAuC;AACpD,WAAO,MAAM,UAAU,aAAa,YAAY;AAC9C,YAAM,aAAa,EAAE,KAAK;AAC1B,YAAM,SAAS,MAAM,OAAO,YAAY,OAAO;AAC/C,aAAO,KAAK,aAAa,QAAQ,UAAU;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EAEA,iBAAuB;AACrB,SAAK,wBAAwB,EAAE,KAAK;AACpC,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEA,YAAY,OAAsB;AAChC,SAAK,aAAa,OAAO,MAAM,KAAK,GAAG,EAAE,KAAK,cAAc;AAAA,EAC9D;AAAA,EAEA,IAAO,QAAwC;AAC7C,UAAM,QAAQ,IAAI,YAAc;AAChC,SAAK,SAAS,CAAC,UAAU;AACvB,YAAM,IAAI,OAAO,KAAK,CAAC;AAAA,IACzB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,UAAoF;AAC3F,WAAO,KAAK,cAAc,CAAC,EAAE,OAAO,YAAY,MAAM;AACpD,UAAI,MAAM,WAAW,MAAM;AACzB,iBAAS,MAAM,MAAM,WAAW;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,cAAc,UAAyE;AACrF,UAAM,OAAO,aAAa;AAC1B,SAAK,WAAW,IAAI,MAAM,QAAQ;AAClC,WAAO;AAAA,MACL,aAAa,MAAM;AACjB,aAAK,WAAW,OAAO,IAAI;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,UAAoF;AAC7F,UAAM,EAAE,YAAY,IAAI,KAAK,SAAS,IAAI,SAAS;AACjD,kBAAY;AACZ,eAAS,GAAG,IAAI;AAAA,IAClB,CAAC;AACD,WAAO,EAAE,YAAY;AAAA,EACvB;AAAA,EAEA,gBAAgB,UAAyE;AACvF,UAAM,EAAE,YAAY,IAAI,KAAK,cAAc,IAAI,SAAS;AACtD,kBAAY;AACZ,eAAS,GAAG,IAAI;AAAA,IAClB,CAAC;AACD,WAAO,EAAE,YAAY;AAAA,EACvB;AACF;", "names": []}