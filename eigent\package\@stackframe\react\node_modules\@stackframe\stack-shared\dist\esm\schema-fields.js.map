{"version": 3, "sources": ["../../src/schema-fields.ts"], "sourcesContent": ["import * as yup from \"yup\";\nimport { KnownErrors } from \".\";\nimport { isBase64 } from \"./utils/bytes\";\nimport { StackAssertionError, throwErr } from \"./utils/errors\";\nimport { decodeBasicAuthorizationHeader } from \"./utils/http\";\nimport { allProviders } from \"./utils/oauth\";\nimport { deepPlainClone, omit } from \"./utils/objects\";\nimport { isValidUrl } from \"./utils/urls\";\nimport { isUuid } from \"./utils/uuids\";\n\ndeclare module \"yup\" {\n  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions\n  interface StringSchema<TType, TContext, TDefault, TFlags> {\n    nonEmpty(message?: string): StringSchema<TType, TContext, TDefault, TFlags>,\n    empty(): StringSchema<TType, TContext, TDefault, TFlags>,\n  }\n\n  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions\n  interface Schema<TType, TContext, TDefault, TFlags> {\n    getNested<K extends keyof NonNullable<TType>>(path: K): yup.Schema<NonNullable<TType>[K], TContext, TDefault, TFlags>,\n\n    // the default types for concat kinda suck, so let's fix that\n    concat<U extends yup.AnySchema>(schema: U): yup.Schema<Omit<NonNullable<TType>, keyof yup.InferType<U>> & yup.InferType<U> | (TType & (null | undefined)), TContext, TDefault, TFlags>,\n  }\n}\n\n// eslint-disable-next-line no-restricted-syntax\nyup.addMethod(yup.string, \"nonEmpty\", function (message?: string) {\n  return this.test(\n    \"non-empty\",\n    message ?? (({ path }) => `${path} must not be empty`),\n    (value) => {\n      return value !== \"\";\n    }\n  );\n});\n\nyup.addMethod(yup.Schema, \"getNested\", function (path: any) {\n  if (!path.match(/^[a-zA-Z_][a-zA-Z0-9_]*$/)) throw new StackAssertionError(`yupSchema.getNested can currently only be used with alphanumeric keys. Fix this in the future. Provided key: ${path}`);\n  return yup.reach(this, path) as any;\n});\n\nexport async function yupValidate<S extends yup.ISchema<any>>(\n  schema: S,\n  obj: unknown,\n  options?: yup.ValidateOptions & { currentUserId?: string | null }\n): Promise<yup.InferType<S>> {\n  try {\n    return await schema.validate(obj, {\n      ...omit(options ?? {}, ['currentUserId']),\n      context: {\n        ...options?.context,\n        stackAllowUserIdMe: options?.currentUserId !== undefined,\n      },\n    });\n  } catch (error) {\n    if (error instanceof ReplaceFieldWithOwnUserId) {\n      const currentUserId = options?.currentUserId;\n      if (!currentUserId) throw new KnownErrors.CannotGetOwnUserWithoutUser();\n\n      // parse yup path\n      let pathRemaining = error.path;\n      const fieldPath = [];\n      while (pathRemaining.length > 0) {\n        if (pathRemaining.startsWith(\"[\")) {\n          const index = pathRemaining.indexOf(\"]\");\n          if (index < 0) throw new StackAssertionError(\"Invalid path\");\n          fieldPath.push(JSON.parse(pathRemaining.slice(1, index)));\n          pathRemaining = pathRemaining.slice(index + 1);\n        } else {\n          let dotIndex = pathRemaining.indexOf(\".\");\n          if (dotIndex === -1) dotIndex = pathRemaining.length;\n          fieldPath.push(pathRemaining.slice(0, dotIndex));\n          pathRemaining = pathRemaining.slice(dotIndex + 1);\n        }\n      }\n\n      const newObj = deepPlainClone(obj);\n      let it = newObj;\n      for (const field of fieldPath.slice(0, -1)) {\n        if (!Object.prototype.hasOwnProperty.call(it, field)) {\n          throw new StackAssertionError(`Segment ${field} of path ${error.path} not found in object`);\n        }\n        it = (it as any)[field];\n      }\n      (it as any)[fieldPath[fieldPath.length - 1]] = currentUserId;\n\n      return await yupValidate(schema, newObj, options);\n    }\n    throw error;\n  }\n}\n\nconst _idDescription = (identify: string) => `The unique identifier of the ${identify}`;\nconst _displayNameDescription = (identify: string) => `Human-readable ${identify} display name. This is not a unique identifier.`;\nconst _clientMetaDataDescription = (identify: string) => `Client metadata. Used as a data store, accessible from the client side. Do not store information that should not be exposed to the client.`;\nconst _clientReadOnlyMetaDataDescription = (identify: string) => `Client read-only, server-writable metadata. Used as a data store, accessible from the client side. Do not store information that should not be exposed to the client. The client can read this data, but cannot modify it. This is useful for things like subscription status.`;\nconst _profileImageUrlDescription = (identify: string) => `URL of the profile image for ${identify}. Can be a Base64 encoded image. Must be smaller than 100KB. Please compress and crop to a square before passing in.`;\nconst _serverMetaDataDescription = (identify: string) => `Server metadata. Used as a data store, only accessible from the server side. You can store secret information related to the ${identify} here.`;\nconst _atMillisDescription = (identify: string) => `(the number of milliseconds since epoch, January 1, 1970, UTC)`;\nconst _createdAtMillisDescription = (identify: string) => `The time the ${identify} was created ${_atMillisDescription(identify)}`;\nconst _signedUpAtMillisDescription = `The time the user signed up ${_atMillisDescription}`;\nconst _lastActiveAtMillisDescription = `The time the user was last active ${_atMillisDescription}`;\n\n\ndeclare const StackAdaptSentinel: unique symbol;\nexport type StackAdaptSentinel = typeof StackAdaptSentinel;\n\n// Built-in replacements\nexport function yupString<A extends string, B extends yup.Maybe<yup.AnyObject> = yup.AnyObject>(...args: Parameters<typeof yup.string<A, B>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.string(...args);\n}\nexport function yupNumber<A extends number, B extends yup.Maybe<yup.AnyObject> = yup.AnyObject>(...args: Parameters<typeof yup.number<A, B>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.number(...args);\n}\nexport function yupBoolean<A extends boolean, B extends yup.Maybe<yup.AnyObject> = yup.AnyObject>(...args: Parameters<typeof yup.boolean<A, B>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.boolean(...args);\n}\n/**\n * @deprecated, use number of milliseconds since epoch instead\n */\nexport function yupDate<A extends Date, B extends yup.Maybe<yup.AnyObject> = yup.AnyObject>(...args: Parameters<typeof yup.date<A, B>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.date(...args);\n}\nexport function yupMixed<A extends {}>(...args: Parameters<typeof yup.mixed<A>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.mixed(...args);\n}\nexport function yupArray<A extends yup.Maybe<yup.AnyObject> = yup.AnyObject, B = any>(...args: Parameters<typeof yup.array<A, B>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.array(...args);\n}\nexport function yupTuple<T extends [unknown, ...unknown[]]>(...args: Parameters<typeof yup.tuple<T>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  return yup.tuple<T>(...args);\n}\nexport function yupObject<A extends yup.Maybe<yup.AnyObject>, B extends yup.ObjectShape>(...args: Parameters<typeof yup.object<A, B>>) {\n  // eslint-disable-next-line no-restricted-syntax\n  const object = yup.object(...args).test(\n    'no-unknown-object-properties',\n    ({ path }) => `${path} contains unknown properties`,\n    (value: any, context) => {\n      if (context.options.context?.noUnknownPathPrefixes?.some((prefix: string) => context.path.startsWith(prefix))) {\n        if (context.schema.spec.noUnknown !== false) {\n          const availableKeys = new Set(Object.keys(context.schema.fields));\n          const unknownKeys = Object.keys(value ?? {}).filter(key => !availableKeys.has(key));\n          if (unknownKeys.length > 0) {\n            // TODO \"did you mean XYZ\"\n            return context.createError({\n              message: `${context.path || \"Object\"} contains unknown properties: ${unknownKeys.join(', ')}`,\n              path: context.path,\n              params: { unknownKeys, availableKeys },\n            });\n          }\n        }\n      }\n      return true;\n    },\n  );\n\n  // we don't want to update the type of `object` to have a default flag\n  return object.default(undefined) as any as typeof object;\n}\n\nexport function yupNever(): yup.MixedSchema<never> {\n  return yupMixed().test('never', 'This value should never be reached', () => false) as any;\n}\n\nexport function yupUnion<T extends yup.ISchema<any>[]>(...args: T): yup.MixedSchema<yup.InferType<T[number]>> {\n  if (args.length === 0) throw new Error('yupUnion must have at least one schema');\n\n  const [first] = args;\n  const firstDesc = first.describe();\n  for (const schema of args) {\n    const desc = schema.describe();\n    if (desc.type !== firstDesc.type) throw new StackAssertionError(`yupUnion must have schemas of the same type (got: ${firstDesc.type} and ${desc.type})`, { first, schema, firstDesc, desc });\n  }\n\n  return yupMixed().test('is-one-of', 'Invalid value', async (value, context) => {\n    const errors = [];\n    for (const schema of args) {\n      try {\n        await yupValidate(schema, value, context.options);\n        return true;\n      } catch (e) {\n        errors.push(e);\n      }\n    }\n    return context.createError({\n      message: `${context.path} is not matched by any of the provided schemas:\\n${errors.map((e: any, i) => '\\tSchema ' + i + \": \\n\\t\\t\" + e.errors.join('\\n\\t\\t')).join('\\n')}`,\n      path: context.path,\n    });\n  });\n}\n\nexport function yupRecord<K extends yup.StringSchema, T extends yup.AnySchema>(\n  keySchema: K,\n  valueSchema: T,\n): yup.MixedSchema<Record<string, yup.InferType<T>>> {\n  return yupObject().unknown(true).test(\n    'record',\n    '${path} must be a record of valid values',\n    async function (value: unknown, context: yup.TestContext) {\n      if (value == null) return true;\n      const { path, createError } = this as any;\n      if (typeof value !== 'object') {\n        return createError({ message: `${path} must be an object` });\n      }\n\n      // Validate each property using the provided valueSchema\n      for (const key of Object.keys(value)) {\n        // Validate the key\n        await yupValidate(keySchema, key, context.options);\n\n        // Validate the value\n        try {\n          await yupValidate(valueSchema, (value as Record<string, unknown>)[key], {\n            ...context.options,\n            context: {\n              ...context.options.context,\n              path: path ? `${path}.${key}` : key,\n            },\n          });\n        } catch (e: any) {\n          return createError({\n            path: path ? `${path}.${key}` : key,\n            message: e.message,\n          });\n        }\n      }\n\n      return true;\n    },\n  ) as any;\n}\n\nexport function ensureObjectSchema<T extends yup.AnyObject>(schema: yup.Schema<T>): yup.ObjectSchema<T> & typeof schema {\n  if (!(schema instanceof yup.ObjectSchema)) throw new StackAssertionError(`assertObjectSchema: schema is not an ObjectSchema: ${schema.describe().type}`);\n  return schema as any;\n}\n\n// Common\nexport const adaptSchema = yupMixed<StackAdaptSentinel>();\n/**\n * Yup's URL schema does not recognize some URLs (including `http://localhost`) as a valid URL. This schema is a workaround for that.\n */\nexport const urlSchema = yupString().test({\n  name: 'no-spaces',\n  message: (params) => `${params.path} contains spaces`,\n  test: (value) => value == null || !value.includes(' ')\n}).test({\n  name: 'url',\n  message: (params) => `${params.path} is not a valid URL`,\n  test: (value) => value == null || isValidUrl(value)\n});\nexport const jsonSchema = yupMixed().nullable().defined().transform((value) => JSON.parse(JSON.stringify(value)));\nexport const jsonStringSchema = yupString().test(\"json\", (params) => `${params.path} is not valid JSON`, (value) => {\n  if (value == null) return true;\n  try {\n    JSON.parse(value);\n    return true;\n  } catch (error) {\n    return false;\n  }\n});\nexport const jsonStringOrEmptySchema = yupString().test(\"json\", (params) => `${params.path} is not valid JSON`, (value) => {\n  if (!value) return true;\n  try {\n    JSON.parse(value);\n    return true;\n  } catch (error) {\n    return false;\n  }\n});\nexport const base64Schema = yupString().test(\"is-base64\", (params) => `${params.path} is not valid base64`, (value) => {\n  if (value == null) return true;\n  return isBase64(value);\n});\nexport const passwordSchema = yupString().max(70);\n\n/**\n * A stricter email schema that does some additional checks for UX input. (Some emails are allowed by the spec, for\n * example `test@localhost` or `abc@gmail`, but almost certainly a user input error.)\n *\n * Note that some users in the DB have an email that doesn't match this regex, so most of the time you should use\n * `emailSchema` instead until we do the DB migration.\n */\n// eslint-disable-next-line no-restricted-syntax\nexport const strictEmailSchema = (message: string | undefined) => yupString().email(message).matches(/^[^.]+(\\.[^.]+)*@.*\\.[^.][^.]+$/, message);\n// eslint-disable-next-line no-restricted-syntax\nexport const emailSchema = yupString().email();\n\nundefined?.test('strictEmailSchema', ({ expect }) => {\n  const validEmails = [\n    \"<EMAIL>\",\n    \"<EMAIL>\",\n    \"<EMAIL>\",\n    \"<EMAIL>\",\n    \"<EMAIL>\",\n    \"<EMAIL>\",\n    \"%!~&+{}=|`#@domain.test\",\n    \"<EMAIL>\",\n  ];\n  for (const email of validEmails) {\n    expect(strictEmailSchema(undefined).validateSync(email)).toBe(email);\n  }\n  const invalidEmails = [\n    \"test@localhost\",\n    \"test@gmail\",\n    \"<EMAIL>.a\",\n    \"test@gmail.a\",\n    \"<EMAIL>\",\n    \"<EMAIL>\",\n    \".<EMAIL>\",\n  ];\n  for (const email of invalidEmails) {\n    expect(() => strictEmailSchema(undefined).validateSync(email)).toThrow();\n  }\n});\n\n// Request auth\nexport const clientOrHigherAuthTypeSchema = yupString().oneOf(['client', 'server', 'admin']).defined();\nexport const serverOrHigherAuthTypeSchema = yupString().oneOf(['server', 'admin']).defined();\nexport const adminAuthTypeSchema = yupString().oneOf(['admin']).defined();\n\n// Projects\nexport const projectIdSchema = yupString().test((v) => v === undefined || v === \"internal\" || isUuid(v)).meta({ openapiField: { description: _idDescription('project'), exampleValue: 'e0b52f4d-dece-408c-af49-d23061bb0f8d' } });\nexport const projectBranchIdSchema = yupString().nonEmpty().max(255).meta({ openapiField: { description: _idDescription('project branch'), exampleValue: 'main' } });\nexport const projectDisplayNameSchema = yupString().meta({ openapiField: { description: _displayNameDescription('project'), exampleValue: 'MyMusic' } });\nexport const projectDescriptionSchema = yupString().nullable().meta({ openapiField: { description: 'A human readable description of the project', exampleValue: 'A music streaming service' } });\nexport const projectCreatedAtMillisSchema = yupNumber().meta({ openapiField: { description: _createdAtMillisDescription('project'), exampleValue: 1630000000000 } });\nexport const projectUserCountSchema = yupNumber().meta({ openapiField: { description: 'The number of users in this project', exampleValue: 10 } });\nexport const projectIsProductionModeSchema = yupBoolean().meta({ openapiField: { description: 'Whether the project is in production mode', exampleValue: true } });\n// Project config\nexport const projectConfigIdSchema = yupString().meta({ openapiField: { description: _idDescription('project config'), exampleValue: 'd09201f0-54f5-40bd-89ff-6d1815ddad24' } });\nexport const projectAllowLocalhostSchema = yupBoolean().meta({ openapiField: { description: 'Whether localhost is allowed as a domain for this project. Should only be allowed in development mode', exampleValue: true } });\nexport const projectCreateTeamOnSignUpSchema = yupBoolean().meta({ openapiField: { description: 'Whether a team should be created for each user that signs up', exampleValue: true } });\nexport const projectMagicLinkEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether magic link authentication is enabled for this project', exampleValue: true } });\nexport const projectPasskeyEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether passkey authentication is enabled for this project', exampleValue: true } });\nexport const projectClientTeamCreationEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether client users can create teams', exampleValue: true } });\nexport const projectClientUserDeletionEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether client users can delete their own account from the client', exampleValue: true } });\nexport const projectSignUpEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether users can sign up new accounts, or whether they are only allowed to sign in to existing accounts. Regardless of this option, the server API can always create new users with the `POST /users` endpoint.', exampleValue: true } });\nexport const projectCredentialEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether email password authentication is enabled for this project', exampleValue: true } });\n// Project OAuth config\nexport const oauthIdSchema = yupString().oneOf(allProviders).meta({ openapiField: { description: `OAuth provider ID, one of ${allProviders.map(x => `\\`${x}\\``).join(', ')}`, exampleValue: 'google' } });\nexport const oauthEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether the OAuth provider is enabled. If an provider is first enabled, then disabled, it will be shown in the list but with enabled=false', exampleValue: true } });\nexport const oauthTypeSchema = yupString().oneOf(['shared', 'standard']).meta({ openapiField: { description: 'OAuth provider type, one of shared, standard. \"shared\" uses Stack shared OAuth keys and it is only meant for development. \"standard\" uses your own OAuth keys and will show your logo and company name when signing in with the provider.', exampleValue: 'standard' } });\nexport const oauthClientIdSchema = yupString().meta({ openapiField: { description: 'OAuth client ID. Needs to be specified when using type=\"standard\"', exampleValue: 'google-oauth-client-id' } });\nexport const oauthClientSecretSchema = yupString().meta({ openapiField: { description: 'OAuth client secret. Needs to be specified when using type=\"standard\"', exampleValue: 'google-oauth-client-secret' } });\nexport const oauthFacebookConfigIdSchema = yupString().meta({ openapiField: { description: 'The configuration id for Facebook business login (for things like ads and marketing). This is only required if you are using the standard OAuth with Facebook and you are using Facebook business login.' } });\nexport const oauthMicrosoftTenantIdSchema = yupString().meta({ openapiField: { description: 'The Microsoft tenant id for Microsoft directory. This is only required if you are using the standard OAuth with Microsoft and you have an Azure AD tenant.' } });\nexport const oauthAccountMergeStrategySchema = yupString().oneOf(['link_method', 'raise_error', 'allow_duplicates']).meta({ openapiField: { description: 'Determines how to handle OAuth logins that match an existing user by email. `link_method` adds the OAuth method to the existing user. `raise_error` rejects the login with an error. `allow_duplicates` creates a new user.', exampleValue: 'link_method' } });\n// Project email config\nexport const emailTypeSchema = yupString().oneOf(['shared', 'standard']).meta({ openapiField: { description: 'Email provider type, one of shared, standard. \"shared\" uses Stack shared email provider and it is only meant for development. \"standard\" uses your own email server and will have your email address as the sender.', exampleValue: 'standard' } });\nexport const emailSenderNameSchema = yupString().meta({ openapiField: { description: 'Email sender name. Needs to be specified when using type=\"standard\"', exampleValue: 'Stack' } });\nexport const emailHostSchema = yupString().meta({ openapiField: { description: 'Email host. Needs to be specified when using type=\"standard\"', exampleValue: 'smtp.your-domain.com' } });\nexport const emailPortSchema = yupNumber().min(0).max(65535).meta({ openapiField: { description: 'Email port. Needs to be specified when using type=\"standard\"', exampleValue: 587 } });\nexport const emailUsernameSchema = yupString().meta({ openapiField: { description: 'Email username. Needs to be specified when using type=\"standard\"', exampleValue: 'smtp-email' } });\nexport const emailSenderEmailSchema = emailSchema.meta({ openapiField: { description: 'Email sender email. Needs to be specified when using type=\"standard\"', exampleValue: '<EMAIL>' } });\nexport const emailPasswordSchema = passwordSchema.meta({ openapiField: { description: 'Email password. Needs to be specified when using type=\"standard\"', exampleValue: 'your-email-password' } });\n// Project domain config\nexport const handlerPathSchema = yupString().test('is-handler-path', 'Handler path must start with /', (value) => value?.startsWith('/')).meta({ openapiField: { description: 'Handler path. If you did not setup a custom handler path, it should be \"/handler\" by default. It needs to start with /', exampleValue: '/handler' } });\n\n// Users\nexport class ReplaceFieldWithOwnUserId extends Error {\n  constructor(public readonly path: string) {\n    super(`This error should be caught by whoever validated the schema, and the field in the path '${path}' should be replaced with the current user's id. This is a workaround to yup not providing access to the context inside the transform function.`);\n  }\n}\nconst userIdMeSentinelUuid = \"cad564fd-f81b-43f4-b390-98abf3fcc17e\";\nexport const userIdOrMeSchema = yupString().uuid().transform(v => {\n  if (v === \"me\") return userIdMeSentinelUuid;\n  else return v;\n}).test((v, context) => {\n  if (!(\"stackAllowUserIdMe\" in (context.options.context ?? {}))) throw new StackAssertionError('userIdOrMeSchema is not allowed in this context. Make sure you\\'re using yupValidate from schema-fields.ts to validate, instead of schema.validate(...).');\n  if (!context.options.context?.stackAllowUserIdMe) throw new StackAssertionError('userIdOrMeSchema is not allowed in this context. Make sure you\\'re passing in the currentUserId option in yupValidate.');\n  if (v === userIdMeSentinelUuid) throw new ReplaceFieldWithOwnUserId(context.path);\n  return true;\n}).meta({ openapiField: { description: 'The ID of the user, or the special value `me` for the currently authenticated user', exampleValue: '3241a285-8329-4d69-8f3d-316e08cf140c' } });\nexport const userIdSchema = yupString().uuid().meta({ openapiField: { description: _idDescription('user'), exampleValue: '3241a285-8329-4d69-8f3d-316e08cf140c' } });\nexport const primaryEmailSchema = emailSchema.meta({ openapiField: { description: 'Primary email', exampleValue: '<EMAIL>' } });\nexport const primaryEmailAuthEnabledSchema = yupBoolean().meta({ openapiField: { description: 'Whether the primary email is used for authentication. If this is set to `false`, the user will not be able to sign in with the primary email with password or OTP', exampleValue: true } });\nexport const primaryEmailVerifiedSchema = yupBoolean().meta({ openapiField: { description: 'Whether the primary email has been verified to belong to this user', exampleValue: true } });\nexport const userDisplayNameSchema = yupString().nullable().meta({ openapiField: { description: _displayNameDescription('user'), exampleValue: 'John Doe' } });\nexport const selectedTeamIdSchema = yupString().uuid().meta({ openapiField: { description: 'ID of the team currently selected by the user', exampleValue: 'team-id' } });\nexport const profileImageUrlSchema = urlSchema.max(1000000).meta({ openapiField: { description: _profileImageUrlDescription('user'), exampleValue: 'https://example.com/image.jpg' } });\nexport const signedUpAtMillisSchema = yupNumber().meta({ openapiField: { description: _signedUpAtMillisDescription, exampleValue: 1630000000000 } });\nexport const userClientMetadataSchema = jsonSchema.meta({ openapiField: { description: _clientMetaDataDescription('user'), exampleValue: { key: 'value' } } });\nexport const userClientReadOnlyMetadataSchema = jsonSchema.meta({ openapiField: { description: _clientReadOnlyMetaDataDescription('user'), exampleValue: { key: 'value' } } });\nexport const userServerMetadataSchema = jsonSchema.meta({ openapiField: { description: _serverMetaDataDescription('user'), exampleValue: { key: 'value' } } });\nexport const userOAuthProviderSchema = yupObject({\n  id: yupString().defined(),\n  type: yupString().oneOf(allProviders).defined(),\n  provider_user_id: yupString().defined(),\n});\nexport const userLastActiveAtMillisSchema = yupNumber().nullable().meta({ openapiField: { description: _lastActiveAtMillisDescription, exampleValue: 1630000000000 } });\nexport const userPasskeyAuthEnabledSchema = yupBoolean().meta({ openapiField: { hidden: true, description: 'Whether the user has passkeys enabled', exampleValue: false } });\nexport const userOtpAuthEnabledSchema = yupBoolean().meta({ openapiField: { hidden: true, description: 'Whether the user has OTP/magic link enabled. ', exampleValue: true } });\nexport const userOtpAuthEnabledMutationSchema = yupBoolean().meta({ openapiField: { hidden: true, description: 'Whether the user has OTP/magic link enabled. Note that only accounts with verified emails can sign-in with OTP.', exampleValue: true } });\nexport const userHasPasswordSchema = yupBoolean().meta({ openapiField: { hidden: true, description: 'Whether the user has a password set. If the user does not have a password set, they will not be able to sign in with email/password.', exampleValue: true } });\nexport const userPasswordMutationSchema = passwordSchema.nullable().meta({ openapiField: { description: 'Sets the user\\'s password. Doing so revokes all current sessions.', exampleValue: 'my-new-password' } }).max(70);\nexport const userPasswordHashMutationSchema = yupString()\n  .nonEmpty()\n  .meta({ openapiField: { description: 'If `password` is not given, sets the user\\'s password hash to the given string in Modular Crypt Format (ex.: `$2a$10$VIhIOofSMqGdGlL4wzE//e.77dAQGqNtF/1dT7bqCrVtQuInWy2qi`). Doing so revokes all current sessions.' } });  // we don't set an exampleValue here because it's exclusive with the password field and having both would break the generated example\nexport const userTotpSecretMutationSchema = base64Schema.nullable().meta({ openapiField: { description: 'Enables 2FA and sets a TOTP secret for the user. Set to null to disable 2FA.', exampleValue: 'dG90cC1zZWNyZXQ=' } });\n\n// Auth\nexport const signInEmailSchema = strictEmailSchema(undefined).meta({ openapiField: { description: 'The email to sign in with.', exampleValue: '<EMAIL>' } });\nexport const emailOtpSignInCallbackUrlSchema = urlSchema.meta({ openapiField: { description: 'The base callback URL to construct the magic link from. A query parameter `code` with the verification code will be appended to it. The page should then make a request to the `/auth/otp/sign-in` endpoint.', exampleValue: 'https://example.com/handler/magic-link-callback' } });\nexport const emailVerificationCallbackUrlSchema = urlSchema.meta({ openapiField: { description: 'The base callback URL to construct a verification link for the verification e-mail. A query parameter `code` with the verification code will be appended to it. The page should then make a request to the `/contact-channels/verify` endpoint.', exampleValue: 'https://example.com/handler/email-verification' } });\nexport const accessTokenResponseSchema = yupString().meta({ openapiField: { description: 'Short-lived access token that can be used to authenticate the user', exampleValue: 'eyJhmMiJB2TO...diI4QT' } });\nexport const refreshTokenResponseSchema = yupString().meta({ openapiField: { description: 'Long-lived refresh token that can be used to obtain a new access token', exampleValue: 'i8ns3aq2...14y' } });\nexport const signInResponseSchema = yupObject({\n  refresh_token: refreshTokenResponseSchema.defined(),\n  access_token: accessTokenResponseSchema.defined(),\n  is_new_user: yupBoolean().meta({ openapiField: { description: 'Whether the user is a new user', exampleValue: true } }).defined(),\n  user_id: userIdSchema.defined(),\n});\n\n// Permissions\nexport const teamSystemPermissions = [\n  '$update_team',\n  '$delete_team',\n  '$read_members',\n  '$remove_members',\n  '$invite_members',\n  '$manage_api_keys',\n] as const;\nexport const permissionDefinitionIdSchema = yupString()\n  .matches(/^\\$?[a-z0-9_:]+$/, 'Only lowercase letters, numbers, \":\", \"_\" and optional \"$\" at the beginning are allowed')\n  .test('is-system-permission', 'System permissions must start with a dollar sign', (value, ctx) => {\n    if (!value) return true;\n    if (value.startsWith('$') && !teamSystemPermissions.includes(value as any)) {\n      return ctx.createError({ message: 'Invalid system permission' });\n    }\n    return true;\n  })\n  .meta({ openapiField: { description: `The permission ID used to uniquely identify a permission. Can either be a custom permission with lowercase letters, numbers, \\`:\\`, and \\`_\\` characters, or one of the system permissions: ${teamSystemPermissions.map(x => `\\`${x}\\``).join(', ')}`, exampleValue: 'read_secret_info' } });\nexport const customPermissionDefinitionIdSchema = yupString()\n  .matches(/^[a-z0-9_:]+$/, 'Only lowercase letters, numbers, \":\", \"_\" are allowed')\n  .meta({ openapiField: { description: 'The permission ID used to uniquely identify a permission. Can only contain lowercase letters, numbers, \":\", and \"_\" characters', exampleValue: 'read_secret_info' } });\nexport const teamPermissionDescriptionSchema = yupString().meta({ openapiField: { description: 'A human-readable description of the permission', exampleValue: 'Read secret information' } });\nexport const containedPermissionIdsSchema = yupArray(permissionDefinitionIdSchema.defined()).meta({ openapiField: { description: 'The IDs of the permissions that are contained in this permission', exampleValue: ['read_public_info'] } });\n\n// Teams\nexport const teamIdSchema = yupString().uuid().meta({ openapiField: { description: _idDescription('team'), exampleValue: 'ad962777-8244-496a-b6a2-e0c6a449c79e' } });\nexport const teamDisplayNameSchema = yupString().meta({ openapiField: { description: _displayNameDescription('team'), exampleValue: 'My Team' } });\nexport const teamProfileImageUrlSchema = urlSchema.max(1000000).meta({ openapiField: { description: _profileImageUrlDescription('team'), exampleValue: 'https://example.com/image.jpg' } });\nexport const teamClientMetadataSchema = jsonSchema.meta({ openapiField: { description: _clientMetaDataDescription('team'), exampleValue: { key: 'value' } } });\nexport const teamClientReadOnlyMetadataSchema = jsonSchema.meta({ openapiField: { description: _clientReadOnlyMetaDataDescription('team'), exampleValue: { key: 'value' } } });\nexport const teamServerMetadataSchema = jsonSchema.meta({ openapiField: { description: _serverMetaDataDescription('team'), exampleValue: { key: 'value' } } });\nexport const teamCreatedAtMillisSchema = yupNumber().meta({ openapiField: { description: _createdAtMillisDescription('team'), exampleValue: 1630000000000 } });\nexport const teamInvitationEmailSchema = emailSchema.meta({ openapiField: { description: 'The email of the user to invite.', exampleValue: '<EMAIL>' } });\nexport const teamInvitationCallbackUrlSchema = urlSchema.meta({ openapiField: { description: 'The base callback URL to construct an invite link with. A query parameter `code` with the verification code will be appended to it. The page should then make a request to the `/team-invitations/accept` endpoint.', exampleValue: 'https://example.com/handler/team-invitation' } });\nexport const teamCreatorUserIdSchema = userIdOrMeSchema.meta({ openapiField: { description: 'The ID of the creator of the team. If not specified, the user will not be added to the team. Can be either \"me\" or the ID of the user. Only used on the client side.', exampleValue: 'me' } });\n\n// Team member profiles\nexport const teamMemberDisplayNameSchema = yupString().meta({ openapiField: { description: _displayNameDescription('team member') + ' Note that this is separate from the display_name of the user.', exampleValue: 'John Doe' } });\nexport const teamMemberProfileImageUrlSchema = urlSchema.max(1000000).meta({ openapiField: { description: _profileImageUrlDescription('team member'), exampleValue: 'https://example.com/image.jpg' } });\n\n// Contact channels\nexport const contactChannelIdSchema = yupString().uuid().meta({ openapiField: { description: _idDescription('contact channel'), exampleValue: 'b3d396b8-c574-4c80-97b3-50031675ceb2' } });\nexport const contactChannelTypeSchema = yupString().oneOf(['email']).meta({ openapiField: { description: `The type of the contact channel. Currently only \"email\" is supported.`, exampleValue: 'email' } });\nexport const contactChannelValueSchema = yupString().when('type', {\n  is: 'email',\n  then: (schema) => schema.email(),\n}).meta({ openapiField: { description: 'The value of the contact channel. For email, this should be a valid email address.', exampleValue: '<EMAIL>' } });\nexport const contactChannelUsedForAuthSchema = yupBoolean().meta({ openapiField: { description: 'Whether the contact channel is used for authentication. If this is set to `true`, the user will be able to sign in with the contact channel with password or OTP.', exampleValue: true } });\nexport const contactChannelIsVerifiedSchema = yupBoolean().meta({ openapiField: { description: 'Whether the contact channel has been verified. If this is set to `true`, the contact channel has been verified to belong to the user.', exampleValue: true } });\nexport const contactChannelIsPrimarySchema = yupBoolean().meta({ openapiField: { description: 'Whether the contact channel is the primary contact channel. If this is set to `true`, it will be used for authentication and notifications by default.', exampleValue: true } });\n\n// Headers\nexport const basicAuthorizationHeaderSchema = yupString().test('is-basic-authorization-header', 'Authorization header must be in the format \"Basic <base64>\"', (value) => {\n  if (!value) return true;\n  return decodeBasicAuthorizationHeader(value) !== null;\n});\n\n// Neon integration\nexport const neonAuthorizationHeaderSchema = basicAuthorizationHeaderSchema.test('is-neon-authorization-header', 'Invalid client_id:client_secret values; did you use the correct values for the Neon integration?', (value) => {\n  if (!value) return true;\n  const [clientId, clientSecret] = decodeBasicAuthorizationHeader(value) ?? throwErr(`Neon authz header invalid? This should've been validated by basicAuthorizationHeaderSchema: ${value}`);\n  for (const neonClientConfig of JSON.parse(process.env.STACK_NEON_INTEGRATION_CLIENTS_CONFIG || '[]')) {\n    if (clientId === neonClientConfig.client_id && clientSecret === neonClientConfig.client_secret) return true;\n  }\n  return false;\n});\n\n// Utils\nexport function yupDefinedWhen<S extends yup.AnyObject>(\n  schema: S,\n  triggers: Record<string, any>,\n): S {\n  const entries = Object.entries(triggers);\n  return schema.when(entries.map(([key]) => key), {\n    is: (...values: any[]) => entries.every(([key, value], index) => value === values[index]),\n    then: (schema: S) => schema.defined(),\n    otherwise: (schema: S) => schema.optional()\n  });\n}\n\nexport function yupDefinedAndNonEmptyWhen<S extends yup.StringSchema>(\n  schema: S,\n  triggers: Record<string, any>,\n): S {\n  const entries = Object.entries(triggers);\n  return schema.when(entries.map(([key]) => key), {\n    is: (...values: any[]) => entries.every(([key, value], index) => value === values[index]),\n    then: (schema: S) => schema.defined().nonEmpty(),\n    otherwise: (schema: S) => schema.optional()\n  });\n}\n"], "mappings": ";AAAA,YAAY,SAAS;AACrB,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB;AACzB,SAAS,qBAAqB,gBAAgB;AAC9C,SAAS,sCAAsC;AAC/C,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB,YAAY;AACrC,SAAS,kBAAkB;AAC3B,SAAS,cAAc;AAmBnB,cAAc,YAAQ,YAAY,SAAU,SAAkB;AAChE,SAAO,KAAK;AAAA,IACV;AAAA,IACA,YAAY,CAAC,EAAE,KAAK,MAAM,GAAG,IAAI;AAAA,IACjC,CAAC,UAAU;AACT,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACF,CAAC;AAEG,cAAc,YAAQ,aAAa,SAAU,MAAW;AAC1D,MAAI,CAAC,KAAK,MAAM,0BAA0B,EAAG,OAAM,IAAI,oBAAoB,gHAAgH,IAAI,EAAE;AACjM,SAAW,UAAM,MAAM,IAAI;AAC7B,CAAC;AAED,eAAsB,YACpB,QACA,KACA,SAC2B;AAC3B,MAAI;AACF,WAAO,MAAM,OAAO,SAAS,KAAK;AAAA,MAChC,GAAG,KAAK,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC;AAAA,MACxC,SAAS;AAAA,QACP,GAAG,SAAS;AAAA,QACZ,oBAAoB,SAAS,kBAAkB;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH,SAAS,OAAO;AACd,QAAI,iBAAiB,2BAA2B;AAC9C,YAAM,gBAAgB,SAAS;AAC/B,UAAI,CAAC,cAAe,OAAM,IAAI,YAAY,4BAA4B;AAGtE,UAAI,gBAAgB,MAAM;AAC1B,YAAM,YAAY,CAAC;AACnB,aAAO,cAAc,SAAS,GAAG;AAC/B,YAAI,cAAc,WAAW,GAAG,GAAG;AACjC,gBAAM,QAAQ,cAAc,QAAQ,GAAG;AACvC,cAAI,QAAQ,EAAG,OAAM,IAAI,oBAAoB,cAAc;AAC3D,oBAAU,KAAK,KAAK,MAAM,cAAc,MAAM,GAAG,KAAK,CAAC,CAAC;AACxD,0BAAgB,cAAc,MAAM,QAAQ,CAAC;AAAA,QAC/C,OAAO;AACL,cAAI,WAAW,cAAc,QAAQ,GAAG;AACxC,cAAI,aAAa,GAAI,YAAW,cAAc;AAC9C,oBAAU,KAAK,cAAc,MAAM,GAAG,QAAQ,CAAC;AAC/C,0BAAgB,cAAc,MAAM,WAAW,CAAC;AAAA,QAClD;AAAA,MACF;AAEA,YAAM,SAAS,eAAe,GAAG;AACjC,UAAI,KAAK;AACT,iBAAW,SAAS,UAAU,MAAM,GAAG,EAAE,GAAG;AAC1C,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,IAAI,KAAK,GAAG;AACpD,gBAAM,IAAI,oBAAoB,WAAW,KAAK,YAAY,MAAM,IAAI,sBAAsB;AAAA,QAC5F;AACA,aAAM,GAAW,KAAK;AAAA,MACxB;AACA,MAAC,GAAW,UAAU,UAAU,SAAS,CAAC,CAAC,IAAI;AAE/C,aAAO,MAAM,YAAY,QAAQ,QAAQ,OAAO;AAAA,IAClD;AACA,UAAM;AAAA,EACR;AACF;AAEA,IAAM,iBAAiB,CAAC,aAAqB,gCAAgC,QAAQ;AACrF,IAAM,0BAA0B,CAAC,aAAqB,kBAAkB,QAAQ;AAChF,IAAM,6BAA6B,CAAC,aAAqB;AACzD,IAAM,qCAAqC,CAAC,aAAqB;AACjE,IAAM,8BAA8B,CAAC,aAAqB,gCAAgC,QAAQ;AAClG,IAAM,6BAA6B,CAAC,aAAqB,gIAAgI,QAAQ;AACjM,IAAM,uBAAuB,CAAC,aAAqB;AACnD,IAAM,8BAA8B,CAAC,aAAqB,gBAAgB,QAAQ,gBAAgB,qBAAqB,QAAQ,CAAC;AAChI,IAAM,+BAA+B,+BAA+B,oBAAoB;AACxF,IAAM,iCAAiC,qCAAqC,oBAAoB;AAOzF,SAAS,aAAmF,MAA2C;AAE5I,SAAW,WAAO,GAAG,IAAI;AAC3B;AACO,SAAS,aAAmF,MAA2C;AAE5I,SAAW,WAAO,GAAG,IAAI;AAC3B;AACO,SAAS,cAAqF,MAA4C;AAE/I,SAAW,YAAQ,GAAG,IAAI;AAC5B;AAIO,SAAS,WAA+E,MAAyC;AAEtI,SAAW,SAAK,GAAG,IAAI;AACzB;AACO,SAAS,YAA0B,MAAuC;AAE/E,SAAW,UAAM,GAAG,IAAI;AAC1B;AACO,SAAS,YAAyE,MAA0C;AAEjI,SAAW,UAAM,GAAG,IAAI;AAC1B;AACO,SAAS,YAA+C,MAAuC;AAEpG,SAAW,UAAS,GAAG,IAAI;AAC7B;AACO,SAAS,aAA4E,MAA2C;AAErI,QAAMA,UAAa,WAAO,GAAG,IAAI,EAAE;AAAA,IACjC;AAAA,IACA,CAAC,EAAE,KAAK,MAAM,GAAG,IAAI;AAAA,IACrB,CAAC,OAAY,YAAY;AACvB,UAAI,QAAQ,QAAQ,SAAS,uBAAuB,KAAK,CAAC,WAAmB,QAAQ,KAAK,WAAW,MAAM,CAAC,GAAG;AAC7G,YAAI,QAAQ,OAAO,KAAK,cAAc,OAAO;AAC3C,gBAAM,gBAAgB,IAAI,IAAI,OAAO,KAAK,QAAQ,OAAO,MAAM,CAAC;AAChE,gBAAM,cAAc,OAAO,KAAK,SAAS,CAAC,CAAC,EAAE,OAAO,SAAO,CAAC,cAAc,IAAI,GAAG,CAAC;AAClF,cAAI,YAAY,SAAS,GAAG;AAE1B,mBAAO,QAAQ,YAAY;AAAA,cACzB,SAAS,GAAG,QAAQ,QAAQ,QAAQ,iCAAiC,YAAY,KAAK,IAAI,CAAC;AAAA,cAC3F,MAAM,QAAQ;AAAA,cACd,QAAQ,EAAE,aAAa,cAAc;AAAA,YACvC,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAGA,SAAOA,QAAO,QAAQ,MAAS;AACjC;AAEO,SAAS,WAAmC;AACjD,SAAO,SAAS,EAAE,KAAK,SAAS,sCAAsC,MAAM,KAAK;AACnF;AAEO,SAAS,YAA0C,MAAoD;AAC5G,MAAI,KAAK,WAAW,EAAG,OAAM,IAAI,MAAM,wCAAwC;AAE/E,QAAM,CAAC,KAAK,IAAI;AAChB,QAAM,YAAY,MAAM,SAAS;AACjC,aAAW,UAAU,MAAM;AACzB,UAAM,OAAO,OAAO,SAAS;AAC7B,QAAI,KAAK,SAAS,UAAU,KAAM,OAAM,IAAI,oBAAoB,qDAAqD,UAAU,IAAI,QAAQ,KAAK,IAAI,KAAK,EAAE,OAAO,QAAQ,WAAW,KAAK,CAAC;AAAA,EAC7L;AAEA,SAAO,SAAS,EAAE,KAAK,aAAa,iBAAiB,OAAO,OAAO,YAAY;AAC7E,UAAM,SAAS,CAAC;AAChB,eAAW,UAAU,MAAM;AACzB,UAAI;AACF,cAAM,YAAY,QAAQ,OAAO,QAAQ,OAAO;AAChD,eAAO;AAAA,MACT,SAAS,GAAG;AACV,eAAO,KAAK,CAAC;AAAA,MACf;AAAA,IACF;AACA,WAAO,QAAQ,YAAY;AAAA,MACzB,SAAS,GAAG,QAAQ,IAAI;AAAA,EAAoD,OAAO,IAAI,CAAC,GAAQ,MAAM,aAAc,IAAI,WAAa,EAAE,OAAO,KAAK,MAAQ,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,MACxK,MAAM,QAAQ;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH;AAEO,SAAS,UACd,WACA,aACmD;AACnD,SAAO,UAAU,EAAE,QAAQ,IAAI,EAAE;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,eAAgB,OAAgB,SAA0B;AACxD,UAAI,SAAS,KAAM,QAAO;AAC1B,YAAM,EAAE,MAAM,YAAY,IAAI;AAC9B,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,YAAY,EAAE,SAAS,GAAG,IAAI,qBAAqB,CAAC;AAAA,MAC7D;AAGA,iBAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AAEpC,cAAM,YAAY,WAAW,KAAK,QAAQ,OAAO;AAGjD,YAAI;AACF,gBAAM,YAAY,aAAc,MAAkC,GAAG,GAAG;AAAA,YACtE,GAAG,QAAQ;AAAA,YACX,SAAS;AAAA,cACP,GAAG,QAAQ,QAAQ;AAAA,cACnB,MAAM,OAAO,GAAG,IAAI,IAAI,GAAG,KAAK;AAAA,YAClC;AAAA,UACF,CAAC;AAAA,QACH,SAAS,GAAQ;AACf,iBAAO,YAAY;AAAA,YACjB,MAAM,OAAO,GAAG,IAAI,IAAI,GAAG,KAAK;AAAA,YAChC,SAAS,EAAE;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAAS,mBAA4C,QAA4D;AACtH,MAAI,EAAE,kBAAsB,kBAAe,OAAM,IAAI,oBAAoB,sDAAsD,OAAO,SAAS,EAAE,IAAI,EAAE;AACvJ,SAAO;AACT;AAGO,IAAM,cAAc,SAA6B;AAIjD,IAAM,YAAY,UAAU,EAAE,KAAK;AAAA,EACxC,MAAM;AAAA,EACN,SAAS,CAAC,WAAW,GAAG,OAAO,IAAI;AAAA,EACnC,MAAM,CAAC,UAAU,SAAS,QAAQ,CAAC,MAAM,SAAS,GAAG;AACvD,CAAC,EAAE,KAAK;AAAA,EACN,MAAM;AAAA,EACN,SAAS,CAAC,WAAW,GAAG,OAAO,IAAI;AAAA,EACnC,MAAM,CAAC,UAAU,SAAS,QAAQ,WAAW,KAAK;AACpD,CAAC;AACM,IAAM,aAAa,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,UAAU,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC;AACzG,IAAM,mBAAmB,UAAU,EAAE,KAAK,QAAQ,CAAC,WAAW,GAAG,OAAO,IAAI,sBAAsB,CAAC,UAAU;AAClH,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI;AACF,SAAK,MAAM,KAAK;AAChB,WAAO;AAAA,EACT,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AACF,CAAC;AACM,IAAM,0BAA0B,UAAU,EAAE,KAAK,QAAQ,CAAC,WAAW,GAAG,OAAO,IAAI,sBAAsB,CAAC,UAAU;AACzH,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI;AACF,SAAK,MAAM,KAAK;AAChB,WAAO;AAAA,EACT,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AACF,CAAC;AACM,IAAM,eAAe,UAAU,EAAE,KAAK,aAAa,CAAC,WAAW,GAAG,OAAO,IAAI,wBAAwB,CAAC,UAAU;AACrH,MAAI,SAAS,KAAM,QAAO;AAC1B,SAAO,SAAS,KAAK;AACvB,CAAC;AACM,IAAM,iBAAiB,UAAU,EAAE,IAAI,EAAE;AAUzC,IAAM,oBAAoB,CAAC,YAAgC,UAAU,EAAE,MAAM,OAAO,EAAE,QAAQ,mCAAmC,OAAO;AAExI,IAAM,cAAc,UAAU,EAAE,MAAM;AA+BtC,IAAM,+BAA+B,UAAU,EAAE,MAAM,CAAC,UAAU,UAAU,OAAO,CAAC,EAAE,QAAQ;AAC9F,IAAM,+BAA+B,UAAU,EAAE,MAAM,CAAC,UAAU,OAAO,CAAC,EAAE,QAAQ;AACpF,IAAM,sBAAsB,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,QAAQ;AAGjE,IAAM,kBAAkB,UAAU,EAAE,KAAK,CAAC,MAAM,MAAM,UAAa,MAAM,cAAc,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,eAAe,SAAS,GAAG,cAAc,uCAAuC,EAAE,CAAC;AACzN,IAAM,wBAAwB,UAAU,EAAE,SAAS,EAAE,IAAI,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,eAAe,gBAAgB,GAAG,cAAc,OAAO,EAAE,CAAC;AAC5J,IAAM,2BAA2B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,wBAAwB,SAAS,GAAG,cAAc,UAAU,EAAE,CAAC;AAChJ,IAAM,2BAA2B,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,+CAA+C,cAAc,4BAA4B,EAAE,CAAC;AACxL,IAAM,+BAA+B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,4BAA4B,SAAS,GAAG,cAAc,OAAc,EAAE,CAAC;AAC5J,IAAM,yBAAyB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,uCAAuC,cAAc,GAAG,EAAE,CAAC;AAC1I,IAAM,gCAAgC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,6CAA6C,cAAc,KAAK,EAAE,CAAC;AAE1J,IAAM,wBAAwB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,eAAe,gBAAgB,GAAG,cAAc,uCAAuC,EAAE,CAAC;AACxK,IAAM,8BAA8B,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,yGAAyG,cAAc,KAAK,EAAE,CAAC;AACpN,IAAM,kCAAkC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,gEAAgE,cAAc,KAAK,EAAE,CAAC;AAC/K,IAAM,gCAAgC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,iEAAiE,cAAc,KAAK,EAAE,CAAC;AAC9K,IAAM,8BAA8B,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,8DAA8D,cAAc,KAAK,EAAE,CAAC;AACzK,IAAM,yCAAyC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,yCAAyC,cAAc,KAAK,EAAE,CAAC;AAC/J,IAAM,yCAAyC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,qEAAqE,cAAc,KAAK,EAAE,CAAC;AAC3L,IAAM,6BAA6B,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,oNAAoN,cAAc,KAAK,EAAE,CAAC;AAC9T,IAAM,iCAAiC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,qEAAqE,cAAc,KAAK,EAAE,CAAC;AAEnL,IAAM,gBAAgB,UAAU,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,6BAA6B,aAAa,IAAI,OAAK,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,cAAc,SAAS,EAAE,CAAC;AACjM,IAAM,qBAAqB,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,8IAA8I,cAAc,KAAK,EAAE,CAAC;AAChP,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,UAAU,UAAU,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,6OAA6O,cAAc,WAAW,EAAE,CAAC;AAC/W,IAAM,sBAAsB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,qEAAqE,cAAc,yBAAyB,EAAE,CAAC;AAC3L,IAAM,0BAA0B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,yEAAyE,cAAc,6BAA6B,EAAE,CAAC;AACvM,IAAM,8BAA8B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,2MAA2M,EAAE,CAAC;AAClS,IAAM,+BAA+B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,6JAA6J,EAAE,CAAC;AACrP,IAAM,kCAAkC,UAAU,EAAE,MAAM,CAAC,eAAe,eAAe,kBAAkB,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,+NAA+N,cAAc,cAAc,EAAE,CAAC;AAEhZ,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,UAAU,UAAU,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,uNAAuN,cAAc,WAAW,EAAE,CAAC;AACzV,IAAM,wBAAwB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,uEAAuE,cAAc,QAAQ,EAAE,CAAC;AAC9K,IAAM,kBAAkB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,gEAAgE,cAAc,uBAAuB,EAAE,CAAC;AAChL,IAAM,kBAAkB,UAAU,EAAE,IAAI,CAAC,EAAE,IAAI,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,gEAAgE,cAAc,IAAI,EAAE,CAAC;AAC/K,IAAM,sBAAsB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,oEAAoE,cAAc,aAAa,EAAE,CAAC;AAC9K,IAAM,yBAAyB,YAAY,KAAK,EAAE,cAAc,EAAE,aAAa,wEAAwE,cAAc,0BAA0B,EAAE,CAAC;AAClM,IAAM,sBAAsB,eAAe,KAAK,EAAE,cAAc,EAAE,aAAa,oEAAoE,cAAc,sBAAsB,EAAE,CAAC;AAE1L,IAAM,oBAAoB,UAAU,EAAE,KAAK,mBAAmB,kCAAkC,CAAC,UAAU,OAAO,WAAW,GAAG,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,0HAA0H,cAAc,WAAW,EAAE,CAAC;AAG7T,IAAM,4BAAN,cAAwC,MAAM;AAAA,EACnD,YAA4B,MAAc;AACxC,UAAM,2FAA2F,IAAI,iJAAiJ;AAD5N;AAAA,EAE5B;AACF;AACA,IAAM,uBAAuB;AACtB,IAAM,mBAAmB,UAAU,EAAE,KAAK,EAAE,UAAU,OAAK;AAChE,MAAI,MAAM,KAAM,QAAO;AAAA,MAClB,QAAO;AACd,CAAC,EAAE,KAAK,CAAC,GAAG,YAAY;AACtB,MAAI,EAAE,yBAAyB,QAAQ,QAAQ,WAAW,CAAC,IAAK,OAAM,IAAI,oBAAoB,yJAA0J;AACxP,MAAI,CAAC,QAAQ,QAAQ,SAAS,mBAAoB,OAAM,IAAI,oBAAoB,uHAAwH;AACxM,MAAI,MAAM,qBAAsB,OAAM,IAAI,0BAA0B,QAAQ,IAAI;AAChF,SAAO;AACT,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,sFAAsF,cAAc,uCAAuC,EAAE,CAAC;AAC9K,IAAM,eAAe,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,eAAe,MAAM,GAAG,cAAc,uCAAuC,EAAE,CAAC;AAC5J,IAAM,qBAAqB,YAAY,KAAK,EAAE,cAAc,EAAE,aAAa,iBAAiB,cAAc,sBAAsB,EAAE,CAAC;AACnI,IAAM,gCAAgC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,qKAAqK,cAAc,KAAK,EAAE,CAAC;AAClR,IAAM,6BAA6B,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,sEAAsE,cAAc,KAAK,EAAE,CAAC;AAChL,IAAM,wBAAwB,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,wBAAwB,MAAM,GAAG,cAAc,WAAW,EAAE,CAAC;AACtJ,IAAM,uBAAuB,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,iDAAiD,cAAc,UAAU,EAAE,CAAC;AAChK,IAAM,wBAAwB,UAAU,IAAI,GAAO,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,4BAA4B,MAAM,GAAG,cAAc,gCAAgC,EAAE,CAAC;AAC/K,IAAM,yBAAyB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,8BAA8B,cAAc,OAAc,EAAE,CAAC;AAC5I,IAAM,2BAA2B,WAAW,KAAK,EAAE,cAAc,EAAE,aAAa,2BAA2B,MAAM,GAAG,cAAc,EAAE,KAAK,QAAQ,EAAE,EAAE,CAAC;AACtJ,IAAM,mCAAmC,WAAW,KAAK,EAAE,cAAc,EAAE,aAAa,mCAAmC,MAAM,GAAG,cAAc,EAAE,KAAK,QAAQ,EAAE,EAAE,CAAC;AACtK,IAAM,2BAA2B,WAAW,KAAK,EAAE,cAAc,EAAE,aAAa,2BAA2B,MAAM,GAAG,cAAc,EAAE,KAAK,QAAQ,EAAE,EAAE,CAAC;AACtJ,IAAM,0BAA0B,UAAU;AAAA,EAC/C,IAAI,UAAU,EAAE,QAAQ;AAAA,EACxB,MAAM,UAAU,EAAE,MAAM,YAAY,EAAE,QAAQ;AAAA,EAC9C,kBAAkB,UAAU,EAAE,QAAQ;AACxC,CAAC;AACM,IAAM,+BAA+B,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,gCAAgC,cAAc,OAAc,EAAE,CAAC;AAC/J,IAAM,+BAA+B,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,MAAM,aAAa,yCAAyC,cAAc,MAAM,EAAE,CAAC;AACpK,IAAM,2BAA2B,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,MAAM,aAAa,iDAAiD,cAAc,KAAK,EAAE,CAAC;AACvK,IAAM,mCAAmC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,MAAM,aAAa,mHAAmH,cAAc,KAAK,EAAE,CAAC;AACjP,IAAM,wBAAwB,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,MAAM,aAAa,wIAAwI,cAAc,KAAK,EAAE,CAAC;AAC3P,IAAM,6BAA6B,eAAe,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,oEAAqE,cAAc,kBAAkB,EAAE,CAAC,EAAE,IAAI,EAAE;AACjN,IAAM,iCAAiC,UAAU,EACrD,SAAS,EACT,KAAK,EAAE,cAAc,EAAE,aAAa,sNAAuN,EAAE,CAAC;AAC1P,IAAM,+BAA+B,aAAa,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,gFAAgF,cAAc,mBAAmB,EAAE,CAAC;AAGrN,IAAM,oBAAoB,kBAAkB,MAAS,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,8BAA8B,cAAc,sBAAsB,EAAE,CAAC;AAChK,IAAM,kCAAkC,UAAU,KAAK,EAAE,cAAc,EAAE,aAAa,gNAAgN,cAAc,kDAAkD,EAAE,CAAC;AACzW,IAAM,qCAAqC,UAAU,KAAK,EAAE,cAAc,EAAE,aAAa,mPAAmP,cAAc,iDAAiD,EAAE,CAAC;AAC9Y,IAAM,4BAA4B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,sEAAsE,cAAc,wBAAwB,EAAE,CAAC;AACjM,IAAM,6BAA6B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,0EAA0E,cAAc,iBAAiB,EAAE,CAAC;AAC/L,IAAM,uBAAuB,UAAU;AAAA,EAC5C,eAAe,2BAA2B,QAAQ;AAAA,EAClD,cAAc,0BAA0B,QAAQ;AAAA,EAChD,aAAa,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,kCAAkC,cAAc,KAAK,EAAE,CAAC,EAAE,QAAQ;AAAA,EAChI,SAAS,aAAa,QAAQ;AAChC,CAAC;AAGM,IAAM,wBAAwB;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACO,IAAM,+BAA+B,UAAU,EACnD,QAAQ,oBAAoB,yFAAyF,EACrH,KAAK,wBAAwB,oDAAoD,CAAC,OAAO,QAAQ;AAChG,MAAI,CAAC,MAAO,QAAO;AACnB,MAAI,MAAM,WAAW,GAAG,KAAK,CAAC,sBAAsB,SAAS,KAAY,GAAG;AAC1E,WAAO,IAAI,YAAY,EAAE,SAAS,4BAA4B,CAAC;AAAA,EACjE;AACA,SAAO;AACT,CAAC,EACA,KAAK,EAAE,cAAc,EAAE,aAAa,+LAA+L,sBAAsB,IAAI,OAAK,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,cAAc,mBAAmB,EAAE,CAAC;AAC5T,IAAM,qCAAqC,UAAU,EACzD,QAAQ,iBAAiB,uDAAuD,EAChF,KAAK,EAAE,cAAc,EAAE,aAAa,kIAAkI,cAAc,mBAAmB,EAAE,CAAC;AACtM,IAAM,kCAAkC,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,kDAAkD,cAAc,0BAA0B,EAAE,CAAC;AACrL,IAAM,+BAA+B,SAAS,6BAA6B,QAAQ,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,oEAAoE,cAAc,CAAC,kBAAkB,EAAE,EAAE,CAAC;AAGpO,IAAM,eAAe,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,eAAe,MAAM,GAAG,cAAc,uCAAuC,EAAE,CAAC;AAC5J,IAAM,wBAAwB,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,wBAAwB,MAAM,GAAG,cAAc,UAAU,EAAE,CAAC;AAC1I,IAAM,4BAA4B,UAAU,IAAI,GAAO,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,4BAA4B,MAAM,GAAG,cAAc,gCAAgC,EAAE,CAAC;AACnL,IAAM,2BAA2B,WAAW,KAAK,EAAE,cAAc,EAAE,aAAa,2BAA2B,MAAM,GAAG,cAAc,EAAE,KAAK,QAAQ,EAAE,EAAE,CAAC;AACtJ,IAAM,mCAAmC,WAAW,KAAK,EAAE,cAAc,EAAE,aAAa,mCAAmC,MAAM,GAAG,cAAc,EAAE,KAAK,QAAQ,EAAE,EAAE,CAAC;AACtK,IAAM,2BAA2B,WAAW,KAAK,EAAE,cAAc,EAAE,aAAa,2BAA2B,MAAM,GAAG,cAAc,EAAE,KAAK,QAAQ,EAAE,EAAE,CAAC;AACtJ,IAAM,4BAA4B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,4BAA4B,MAAM,GAAG,cAAc,OAAc,EAAE,CAAC;AACtJ,IAAM,4BAA4B,YAAY,KAAK,EAAE,cAAc,EAAE,aAAa,oCAAoC,cAAc,sBAAsB,EAAE,CAAC;AAC7J,IAAM,kCAAkC,UAAU,KAAK,EAAE,cAAc,EAAE,aAAa,uNAAuN,cAAc,8CAA8C,EAAE,CAAC;AAC5W,IAAM,0BAA0B,iBAAiB,KAAK,EAAE,cAAc,EAAE,aAAa,wKAAwK,cAAc,KAAK,EAAE,CAAC;AAGnR,IAAM,8BAA8B,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,wBAAwB,aAAa,IAAI,kEAAkE,cAAc,WAAW,EAAE,CAAC;AAC3N,IAAM,kCAAkC,UAAU,IAAI,GAAO,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,4BAA4B,aAAa,GAAG,cAAc,gCAAgC,EAAE,CAAC;AAGhM,IAAM,yBAAyB,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,eAAe,iBAAiB,GAAG,cAAc,uCAAuC,EAAE,CAAC;AACjL,IAAM,2BAA2B,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,yEAAyE,cAAc,QAAQ,EAAE,CAAC;AACpM,IAAM,4BAA4B,UAAU,EAAE,KAAK,QAAQ;AAAA,EAChE,IAAI;AAAA,EACJ,MAAM,CAAC,WAAW,OAAO,MAAM;AACjC,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,sFAAsF,cAAc,sBAAsB,EAAE,CAAC;AAC7J,IAAM,kCAAkC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,qKAAqK,cAAc,KAAK,EAAE,CAAC;AACpR,IAAM,iCAAiC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,yIAAyI,cAAc,KAAK,EAAE,CAAC;AACvP,IAAM,gCAAgC,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,0JAA0J,cAAc,KAAK,EAAE,CAAC;AAGvQ,IAAM,iCAAiC,UAAU,EAAE,KAAK,iCAAiC,+DAA+D,CAAC,UAAU;AACxK,MAAI,CAAC,MAAO,QAAO;AACnB,SAAO,+BAA+B,KAAK,MAAM;AACnD,CAAC;AAGM,IAAM,gCAAgC,+BAA+B,KAAK,gCAAgC,oGAAoG,CAAC,UAAU;AAC9N,MAAI,CAAC,MAAO,QAAO;AACnB,QAAM,CAAC,UAAU,YAAY,IAAI,+BAA+B,KAAK,KAAK,SAAS,+FAA+F,KAAK,EAAE;AACzL,aAAW,oBAAoB,KAAK,MAAM,QAAQ,IAAI,yCAAyC,IAAI,GAAG;AACpG,QAAI,aAAa,iBAAiB,aAAa,iBAAiB,iBAAiB,cAAe,QAAO;AAAA,EACzG;AACA,SAAO;AACT,CAAC;AAGM,SAAS,eACd,QACA,UACG;AACH,QAAM,UAAU,OAAO,QAAQ,QAAQ;AACvC,SAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG;AAAA,IAC9C,IAAI,IAAI,WAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,UAAU,OAAO,KAAK,CAAC;AAAA,IACxF,MAAM,CAACC,YAAcA,QAAO,QAAQ;AAAA,IACpC,WAAW,CAACA,YAAcA,QAAO,SAAS;AAAA,EAC5C,CAAC;AACH;AAEO,SAAS,0BACd,QACA,UACG;AACH,QAAM,UAAU,OAAO,QAAQ,QAAQ;AACvC,SAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG;AAAA,IAC9C,IAAI,IAAI,WAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,UAAU,OAAO,KAAK,CAAC;AAAA,IACxF,MAAM,CAACA,YAAcA,QAAO,QAAQ,EAAE,SAAS;AAAA,IAC/C,WAAW,CAACA,YAAcA,QAAO,SAAS;AAAA,EAC5C,CAAC;AACH;", "names": ["object", "schema"]}