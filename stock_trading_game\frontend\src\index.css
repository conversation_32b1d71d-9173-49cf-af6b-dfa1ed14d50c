/* 🐾 模拟炒股游戏 - 全局样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

/* 股票涨跌颜色 */
.stock-up {
  color: #ff4d4f !important;
}

.stock-down {
  color: #52c41a !important;
}

.stock-neutral {
  color: #8c8c8c !important;
}

/* 股票涨跌背景色 */
.stock-up-bg {
  background-color: #fff2f0 !important;
}

.stock-down-bg {
  background-color: #f6ffed !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 数字跳动效果 */
.number-animate {
  transition: all 0.3s ease;
}

.number-animate.up {
  color: #ff4d4f;
  transform: scale(1.1);
}

.number-animate.down {
  color: #52c41a;
  transform: scale(1.1);
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.2s ease;
  cursor: pointer;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}
