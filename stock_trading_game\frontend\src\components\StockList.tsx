import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Input, 
  Select, 
  Space, 
  Typography, 
  Tag, 
  Button,
  Card,
  Row,
  Col,
  Statistic
} from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getStocks } from '../services/api';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

interface Stock {
  id: number;
  code: string;
  name: string;
  industry: string;
  current_price: number;
  opening_price: number;
  previous_close: number;
  change_amount: number;
  change_percent: number;
  highest_price: number;
  lowest_price: number;
  volume: number;
  turnover: number;
  limit_up: number;
  limit_down: number;
  is_active: boolean;
}

const StockList: React.FC = () => {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  useEffect(() => {
    loadStocks();
  }, [pagination.current, pagination.pageSize, searchText, selectedIndustry]);

  const loadStocks = async () => {
    try {
      setLoading(true);
      const params = {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        search: searchText || undefined,
        industry: selectedIndustry || undefined,
      };
      
      const data = await getStocks(params);
      setStocks(data);
      
      // 模拟总数（实际应该从API返回）
      setPagination(prev => ({ ...prev, total: data.length + 100 }));
    } catch (error) {
      console.error('加载股票数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return '#ff4d4f';
    if (change < 0) return '#52c41a';
    return '#8c8c8c';
  };

  const formatNumber = (num: number) => {
    if (num >= 100000000) {
      return `${(num / 100000000).toFixed(2)}亿`;
    } else if (num >= 10000) {
      return `${(num / 10000).toFixed(2)}万`;
    }
    return num.toLocaleString();
  };

  const columns: ColumnsType<Stock> = [
    {
      title: '股票代码',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      render: (code: string) => <strong>{code}</strong>,
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
      width: 100,
      render: (industry: string) => <Tag>{industry}</Tag>,
    },
    {
      title: '现价',
      dataIndex: 'current_price',
      key: 'current_price',
      width: 80,
      align: 'right',
      render: (price: number, record: Stock) => (
        <span style={{ color: getChangeColor(record.change_percent) }}>
          ¥{price.toFixed(2)}
        </span>
      ),
    },
    {
      title: '涨跌额',
      dataIndex: 'change_amount',
      key: 'change_amount',
      width: 80,
      align: 'right',
      render: (amount: number) => (
        <span style={{ color: getChangeColor(amount) }}>
          {amount > 0 ? '+' : ''}{amount.toFixed(2)}
        </span>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: 'change_percent',
      key: 'change_percent',
      width: 80,
      align: 'right',
      render: (percent: number) => (
        <span style={{ color: getChangeColor(percent) }}>
          {percent > 0 ? '+' : ''}{percent.toFixed(2)}%
        </span>
      ),
    },
    {
      title: '最高',
      dataIndex: 'highest_price',
      key: 'highest_price',
      width: 80,
      align: 'right',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '最低',
      dataIndex: 'lowest_price',
      key: 'lowest_price',
      width: 80,
      align: 'right',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 100,
      align: 'right',
      render: (volume: number) => formatNumber(volume),
    },
    {
      title: '成交额',
      dataIndex: 'turnover',
      key: 'turnover',
      width: 100,
      align: 'right',
      render: (turnover: number) => `¥${formatNumber(turnover)}`,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: Stock) => (
        <Space>
          <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
            买入
          </Button>
          <Button type="link" size="small" style={{ color: '#52c41a' }}>
            卖出
          </Button>
        </Space>
      ),
    },
  ];

  // 获取行业列表
  const industries = Array.from(new Set(stocks.map(stock => stock.industry)));

  return (
    <div style={{ padding: 24 }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        📈 股票列表
      </Title>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="搜索股票代码或名称"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={loadStocks}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={6} md={4}>
            <Select
              placeholder="选择行业"
              value={selectedIndustry}
              onChange={setSelectedIndustry}
              allowClear
              style={{ width: '100%' }}
            >
              {industries.map(industry => (
                <Option key={industry} value={industry}>
                  {industry}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4} md={3}>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadStocks}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
          <Col xs={24} sm={6} md={11}>
            <Space>
              <Statistic 
                title="总股票数" 
                value={pagination.total} 
                suffix="只"
                valueStyle={{ fontSize: 16 }}
              />
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 股票表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={stocks}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize || prev.pageSize,
              }));
            },
          }}
          scroll={{ x: 1000 }}
          size="small"
          rowClassName={(record) => {
            if (record.change_percent > 0) return 'stock-up-bg';
            if (record.change_percent < 0) return 'stock-down-bg';
            return '';
          }}
        />
      </Card>
    </div>
  );
};

export default StockList;
