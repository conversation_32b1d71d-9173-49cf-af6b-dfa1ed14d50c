import React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";
import { type VariantProps } from "class-variance-authority";
declare const Label: React.FC<Omit<LabelPrimitive.LabelProps & React.RefAttributes<HTMLLabelElement>, "ref"> & VariantProps<(props?: import("class-variance-authority/types").ClassProp | undefined) => string> & {
    ref?: React.Ref<HTMLLabelElement> | undefined;
}>;
declare const SpanLabel: React.FC<Omit<LabelPrimitive.LabelProps & React.RefAttributes<HTMLLabelElement>, "ref"> & VariantProps<(props?: import("class-variance-authority/types").ClassProp | undefined) => string> & {
    ref?: React.Ref<HTMLLabelElement> | undefined;
}>;
export { Label, SpanLabel };
