{"version": 3, "sources": ["../../../src/utils/json.tsx"], "sourcesContent": ["import { Result } from \"./results\";\n\nexport type Json =\n  | null\n  | boolean\n  | number\n  | string\n  | Json[]\n  | { [key: string]: <PERSON><PERSON> };\n\nexport type ReadonlyJson =\n  | null\n  | boolean\n  | number\n  | string\n  | readonly ReadonlyJson[]\n  | { readonly [key: string]: Readonly<PERSON><PERSON> };\n\nexport function isJson(value: unknown): value is Json {\n  switch (typeof value) {\n    case \"object\": {\n      if (value === null) return true;\n      if (Array.isArray(value)) return value.every(isJson);\n      return Object.keys(value).every(k => typeof k === \"string\") && Object.values(value).every(isJson);\n    }\n    case \"string\":\n    case \"number\":\n    case \"boolean\": {\n      return true;\n    }\n    default: {\n      return false;\n    }\n  }\n}\nundefined?.test(\"isJson\", ({ expect }) => {\n  // Test primitive values\n  expect(isJson(null)).toBe(true);\n  expect(isJson(true)).toBe(true);\n  expect(isJson(false)).toBe(true);\n  expect(isJson(123)).toBe(true);\n  expect(isJson(\"string\")).toBe(true);\n\n  // Test arrays\n  expect(isJson([])).toBe(true);\n  expect(isJson([1, 2, 3])).toBe(true);\n  expect(isJson([\"a\", \"b\", \"c\"])).toBe(true);\n  expect(isJson([1, \"a\", true, null])).toBe(true);\n  expect(isJson([1, [2, 3], { a: \"b\" }])).toBe(true);\n\n  // Test objects\n  expect(isJson({})).toBe(true);\n  expect(isJson({ a: 1, b: 2 })).toBe(true);\n  expect(isJson({ a: \"string\", b: true, c: null })).toBe(true);\n  expect(isJson({ a: [1, 2, 3], b: { c: \"d\" } })).toBe(true);\n\n  // Test invalid JSON values\n  expect(isJson(undefined)).toBe(false);\n  expect(isJson(() => {})).toBe(false);\n  expect(isJson(Symbol())).toBe(false);\n  expect(isJson(BigInt(123))).toBe(false);\n\n  // Test arrays with invalid JSON values\n  expect(isJson([1, undefined, 3])).toBe(false);\n  expect(isJson([1, () => {}, 3])).toBe(false);\n\n  // Test objects with invalid JSON values\n  expect(isJson({ a: 1, b: undefined })).toBe(false);\n  expect(isJson({ a: 1, b: () => {} })).toBe(false);\n});\n\nexport function parseJson(json: string): Result<Json> {\n  return Result.fromThrowing(() => JSON.parse(json));\n}\nundefined?.test(\"parseJson\", ({ expect }) => {\n  // Test valid JSON strings\n  const nullResult = parseJson(\"null\");\n  expect(nullResult.status).toBe(\"ok\");\n  if (nullResult.status === \"ok\") {\n    expect(nullResult.data).toBe(null);\n  }\n\n  const trueResult = parseJson(\"true\");\n  expect(trueResult.status).toBe(\"ok\");\n  if (trueResult.status === \"ok\") {\n    expect(trueResult.data).toBe(true);\n  }\n\n  const numberResult = parseJson(\"123\");\n  expect(numberResult.status).toBe(\"ok\");\n  if (numberResult.status === \"ok\") {\n    expect(numberResult.data).toBe(123);\n  }\n\n  const stringResult = parseJson('\"string\"');\n  expect(stringResult.status).toBe(\"ok\");\n  if (stringResult.status === \"ok\") {\n    expect(stringResult.data).toBe(\"string\");\n  }\n\n  const emptyArrayResult = parseJson(\"[]\");\n  expect(emptyArrayResult.status).toBe(\"ok\");\n  if (emptyArrayResult.status === \"ok\") {\n    expect(emptyArrayResult.data).toEqual([]);\n  }\n\n  const arrayResult = parseJson(\"[1,2,3]\");\n  expect(arrayResult.status).toBe(\"ok\");\n  if (arrayResult.status === \"ok\") {\n    expect(arrayResult.data).toEqual([1, 2, 3]);\n  }\n\n  const emptyObjectResult = parseJson(\"{}\");\n  expect(emptyObjectResult.status).toBe(\"ok\");\n  if (emptyObjectResult.status === \"ok\") {\n    expect(emptyObjectResult.data).toEqual({});\n  }\n\n  const objectResult = parseJson('{\"a\":1,\"b\":\"string\"}');\n  expect(objectResult.status).toBe(\"ok\");\n  if (objectResult.status === \"ok\") {\n    expect(objectResult.data).toEqual({ a: 1, b: \"string\" });\n  }\n\n  // Test invalid JSON strings\n  expect(parseJson(\"\").status).toBe(\"error\");\n  expect(parseJson(\"undefined\").status).toBe(\"error\");\n  expect(parseJson(\"{\").status).toBe(\"error\");\n  expect(parseJson('{\"a\":1,}').status).toBe(\"error\");\n  expect(parseJson(\"function(){}\").status).toBe(\"error\");\n});\n\nexport function stringifyJson(json: Json): Result<string> {\n  return Result.fromThrowing(() => JSON.stringify(json));\n}\nundefined?.test(\"stringifyJson\", ({ expect }) => {\n  // Test primitive values\n  const nullResult = stringifyJson(null);\n  expect(nullResult.status).toBe(\"ok\");\n  if (nullResult.status === \"ok\") {\n    expect(nullResult.data).toBe(\"null\");\n  }\n\n  const trueResult = stringifyJson(true);\n  expect(trueResult.status).toBe(\"ok\");\n  if (trueResult.status === \"ok\") {\n    expect(trueResult.data).toBe(\"true\");\n  }\n\n  const numberResult = stringifyJson(123);\n  expect(numberResult.status).toBe(\"ok\");\n  if (numberResult.status === \"ok\") {\n    expect(numberResult.data).toBe(\"123\");\n  }\n\n  const stringResult = stringifyJson(\"string\");\n  expect(stringResult.status).toBe(\"ok\");\n  if (stringResult.status === \"ok\") {\n    expect(stringResult.data).toBe('\"string\"');\n  }\n\n  // Test arrays\n  const emptyArrayResult = stringifyJson([]);\n  expect(emptyArrayResult.status).toBe(\"ok\");\n  if (emptyArrayResult.status === \"ok\") {\n    expect(emptyArrayResult.data).toBe(\"[]\");\n  }\n\n  const arrayResult = stringifyJson([1, 2, 3]);\n  expect(arrayResult.status).toBe(\"ok\");\n  if (arrayResult.status === \"ok\") {\n    expect(arrayResult.data).toBe(\"[1,2,3]\");\n  }\n\n  // Test objects\n  const emptyObjectResult = stringifyJson({});\n  expect(emptyObjectResult.status).toBe(\"ok\");\n  if (emptyObjectResult.status === \"ok\") {\n    expect(emptyObjectResult.data).toBe(\"{}\");\n  }\n\n  const objectResult = stringifyJson({ a: 1, b: \"string\" });\n  expect(objectResult.status).toBe(\"ok\");\n  if (objectResult.status === \"ok\") {\n    expect(objectResult.data).toBe('{\"a\":1,\"b\":\"string\"}');\n  }\n\n  // Test nested structures\n  const nested = { a: [1, 2, 3], b: { c: \"d\" } };\n  const nestedResult = stringifyJson(nested);\n  expect(nestedResult.status).toBe(\"ok\");\n  if (nestedResult.status === \"ok\") {\n    expect(nestedResult.data).toBe('{\"a\":[1,2,3],\"b\":{\"c\":\"d\"}}');\n  }\n\n  // Test circular references (should error)\n  const circular: any = { a: 1 };\n  circular.self = circular;\n  expect(stringifyJson(circular).status).toBe(\"error\");\n});\n"], "mappings": ";AAAA,SAAS,cAAc;AAkBhB,SAAS,OAAO,OAA+B;AACpD,UAAQ,OAAO,OAAO;AAAA,IACpB,KAAK,UAAU;AACb,UAAI,UAAU,KAAM,QAAO;AAC3B,UAAI,MAAM,QAAQ,KAAK,EAAG,QAAO,MAAM,MAAM,MAAM;AACnD,aAAO,OAAO,KAAK,KAAK,EAAE,MAAM,OAAK,OAAO,MAAM,QAAQ,KAAK,OAAO,OAAO,KAAK,EAAE,MAAM,MAAM;AAAA,IAClG;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,WAAW;AACd,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAqCO,SAAS,UAAU,MAA4B;AACpD,SAAO,OAAO,aAAa,MAAM,KAAK,MAAM,IAAI,CAAC;AACnD;AA2DO,SAAS,cAAc,MAA4B;AACxD,SAAO,OAAO,aAAa,MAAM,KAAK,UAAU,IAAI,CAAC;AACvD;", "names": []}