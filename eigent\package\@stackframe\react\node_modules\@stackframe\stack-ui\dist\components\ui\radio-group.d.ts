import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import React from "react";
declare const RadioGroup: React.FC<Omit<RadioGroupPrimitive.RadioGroupProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const RadioGroupItem: React.FC<Omit<RadioGroupPrimitive.RadioGroupItemProps & React.RefAttributes<HTMLButtonElement>, "ref"> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
}>;
export { RadioGroup, RadioGroupItem };
