"""
🐾 API路由汇总
API Routes Collection

汇总所有API路由
"""

from fastapi import APIRouter

from app.api.v1.endpoints import stocks, users, orders, market

api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(stocks.router, prefix="/stocks", tags=["stocks"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(orders.router, prefix="/orders", tags=["orders"])
api_router.include_router(market.router, prefix="/market", tags=["market"])

@api_router.get("/")
async def api_root():
    """API根路径"""
    return {
        "message": "🐾 模拟炒股游戏 API v1",
        "version": "1.0.0",
        "endpoints": {
            "stocks": "/api/v1/stocks",
            "users": "/api/v1/users", 
            "orders": "/api/v1/orders",
            "market": "/api/v1/market",
            "docs": "/docs",
            "health": "/health"
        }
    }
