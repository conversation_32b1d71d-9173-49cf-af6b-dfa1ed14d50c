"""
🐾 股票市场模拟引擎
Stock Market Simulator

模拟真实A股市场的价格波动和交易机制
"""

import asyncio
import random
import math
from datetime import datetime, time
from typing import Dict, List, Optional, AsyncGenerator
from loguru import logger
import numpy as np

from app.core.config import settings
from app.core.database import AsyncSessionLocal
from app.core.redis_client import redis_client
from app.models.stock import Stock
from app.models.market_data import MarketData, TimeFrame


class MarketSimulator:
    """市场模拟器"""
    
    def __init__(self):
        self.is_running = False
        self.stocks_cache: Dict[int, Stock] = {}
        self.price_history: Dict[int, List[float]] = {}
        self.market_sentiment = 0.0  # 市场情绪 (-1到1)
        self.volatility_multiplier = 1.0  # 波动率乘数
        
        # 市场开放时间
        self.market_open_time = time(settings.MARKET_OPEN_HOUR, settings.MARKET_OPEN_MINUTE)
        self.market_close_time = time(settings.MARKET_CLOSE_HOUR, settings.MARKET_CLOSE_MINUTE)
        
    async def start(self) -> None:
        """启动市场模拟器"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("🎮 市场模拟器启动中...")
        
        # 加载股票数据
        await self._load_stocks()
        
        # 启动价格更新循环
        asyncio.create_task(self._price_update_loop())
        
        # 启动市场情绪更新
        asyncio.create_task(self._market_sentiment_loop())
        
        logger.info("✅ 市场模拟器启动完成")
    
    async def stop(self) -> None:
        """停止市场模拟器"""
        self.is_running = False
        logger.info("🛑 市场模拟器已停止")
    
    async def _load_stocks(self) -> None:
        """加载股票数据到缓存"""
        async with AsyncSessionLocal() as session:
            from sqlalchemy import select
            result = await session.execute(
                select(Stock).where(Stock.is_active == True)
            )
            stocks = result.scalars().all()
            
            for stock in stocks:
                self.stocks_cache[stock.id] = stock
                self.price_history[stock.id] = [stock.current_price]
                
                # 计算涨跌停价格
                stock.calculate_limit_prices()
            
            await session.commit()
            logger.info(f"📊 已加载 {len(stocks)} 只股票到缓存")
    
    async def _price_update_loop(self) -> None:
        """价格更新循环"""
        while self.is_running:
            try:
                if self._is_market_open():
                    await self._update_all_prices()
                    await self._broadcast_market_data()
                
                await asyncio.sleep(settings.MARKET_UPDATE_INTERVAL)
                
            except Exception as e:
                logger.error(f"❌ 价格更新循环错误: {e}")
                await asyncio.sleep(1)
    
    async def _market_sentiment_loop(self) -> None:
        """市场情绪更新循环"""
        while self.is_running:
            try:
                # 每5分钟更新一次市场情绪
                await asyncio.sleep(300)
                await self._update_market_sentiment()
                
            except Exception as e:
                logger.error(f"❌ 市场情绪更新错误: {e}")
    
    def _is_market_open(self) -> bool:
        """检查市场是否开放"""
        now = datetime.now().time()
        return self.market_open_time <= now <= self.market_close_time
    
    async def _update_all_prices(self) -> None:
        """更新所有股票价格"""
        tasks = []
        for stock_id, stock in self.stocks_cache.items():
            if stock.is_tradeable():
                task = self._update_stock_price(stock)
                tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _update_stock_price(self, stock: Stock) -> None:
        """更新单只股票价格"""
        try:
            # 获取当前价格
            current_price = stock.current_price
            
            # 计算新价格
            new_price = self._calculate_new_price(stock)
            
            # 应用涨跌停限制
            new_price = self._apply_price_limits(stock, new_price)
            
            # 更新股票价格
            stock.update_price(new_price)
            
            # 更新价格历史
            self.price_history[stock.id].append(new_price)
            if len(self.price_history[stock.id]) > 1000:  # 保持最近1000个价格点
                self.price_history[stock.id] = self.price_history[stock.id][-1000:]
            
            # 更新技术指标
            stock.update_technical_indicators(self.price_history[stock.id])
            
            # 缓存到Redis
            await self._cache_stock_data(stock)
            
        except Exception as e:
            logger.error(f"❌ 更新股票 {stock.code} 价格失败: {e}")
    
    def _calculate_new_price(self, stock: Stock) -> float:
        """计算新的股票价格"""
        current_price = stock.current_price
        
        # 基础随机波动
        base_volatility = stock.volatility * self.volatility_multiplier
        random_change = np.random.normal(0, base_volatility)
        
        # 市场情绪影响
        sentiment_impact = self.market_sentiment * 0.001
        
        # 新闻事件影响
        news_impact = stock.news_impact * 0.002
        
        # 技术指标影响
        technical_impact = self._calculate_technical_impact(stock)
        
        # 交易量影响
        volume_impact = self._calculate_volume_impact(stock)
        
        # 行业相关性影响
        industry_impact = self._calculate_industry_impact(stock)
        
        # 综合影响
        total_change = (
            random_change + 
            sentiment_impact + 
            news_impact + 
            technical_impact + 
            volume_impact + 
            industry_impact
        )
        
        # 计算新价格
        new_price = current_price * (1 + total_change)
        
        # 确保价格为正数
        return max(0.01, new_price)
    
    def _calculate_technical_impact(self, stock: Stock) -> float:
        """计算技术指标影响"""
        impact = 0.0
        
        # 均线影响
        if stock.ma5 and stock.ma20:
            if stock.current_price > stock.ma5 > stock.ma20:
                impact += 0.0005  # 多头排列，轻微上涨
            elif stock.current_price < stock.ma5 < stock.ma20:
                impact -= 0.0005  # 空头排列，轻微下跌
        
        # RSI影响（如果有的话）
        # 这里可以添加更多技术指标的影响
        
        return impact
    
    def _calculate_volume_impact(self, stock: Stock) -> float:
        """计算交易量影响"""
        # 简化的交易量影响模型
        # 实际应该基于真实的买卖订单
        volume_factor = random.uniform(0.8, 1.2)
        return (volume_factor - 1.0) * 0.001
    
    def _calculate_industry_impact(self, stock: Stock) -> float:
        """计算行业影响"""
        # 同行业股票的相关性影响
        industry_sentiment = random.uniform(-0.002, 0.002)
        return industry_sentiment
    
    def _apply_price_limits(self, stock: Stock, new_price: float) -> float:
        """应用涨跌停限制"""
        if stock.limit_up_price and new_price > stock.limit_up_price:
            return stock.limit_up_price
        
        if stock.limit_down_price and new_price < stock.limit_down_price:
            return stock.limit_down_price
        
        return round(new_price, 2)
    
    async def _update_market_sentiment(self) -> None:
        """更新市场情绪"""
        # 市场情绪的随机游走
        change = np.random.normal(0, 0.1)
        self.market_sentiment = max(-1.0, min(1.0, self.market_sentiment + change))
        
        # 波动率调整
        if abs(self.market_sentiment) > 0.5:
            self.volatility_multiplier = 1.5  # 高情绪时增加波动
        else:
            self.volatility_multiplier = 1.0
        
        logger.debug(f"📊 市场情绪更新: {self.market_sentiment:.3f}, 波动率乘数: {self.volatility_multiplier}")
    
    async def _cache_stock_data(self, stock: Stock) -> None:
        """缓存股票数据到Redis"""
        stock_data = {
            "id": stock.id,
            "code": stock.code,
            "name": stock.name,
            "current_price": stock.current_price,
            "change_amount": stock.change_amount,
            "change_percent": stock.change_percent,
            "volume": stock.volume,
            "updated_at": datetime.now().isoformat()
        }
        
        await redis_client.hset(f"stock:{stock.id}", stock_data)
        await redis_client.expire(f"stock:{stock.id}", 3600)  # 1小时过期
    
    async def _broadcast_market_data(self) -> None:
        """广播市场数据"""
        try:
            # 准备广播数据
            market_data = {
                "timestamp": datetime.now().isoformat(),
                "market_sentiment": self.market_sentiment,
                "stocks": []
            }
            
            for stock in list(self.stocks_cache.values())[:10]:  # 只广播前10只股票
                stock_data = {
                    "id": stock.id,
                    "code": stock.code,
                    "name": stock.name,
                    "current_price": stock.current_price,
                    "change_percent": stock.change_percent,
                    "volume": stock.volume
                }
                market_data["stocks"].append(stock_data)
            
            # 发布到Redis频道
            await redis_client.publish("market_data", market_data)
            
        except Exception as e:
            logger.error(f"❌ 广播市场数据失败: {e}")
    
    async def get_real_time_data(self) -> AsyncGenerator[dict, None]:
        """获取实时市场数据流"""
        while self.is_running:
            try:
                # 构建实时数据
                data = {
                    "timestamp": datetime.now().isoformat(),
                    "market_sentiment": self.market_sentiment,
                    "is_market_open": self._is_market_open(),
                    "stocks": [stock.to_dict() for stock in list(self.stocks_cache.values())[:20]]
                }
                
                yield data
                await asyncio.sleep(settings.MARKET_UPDATE_INTERVAL)
                
            except Exception as e:
                logger.error(f"❌ 获取实时数据失败: {e}")
                await asyncio.sleep(1)
    
    async def get_stock_by_code(self, code: str) -> Optional[Stock]:
        """根据代码获取股票"""
        for stock in self.stocks_cache.values():
            if stock.code == code:
                return stock
        return None
    
    async def trigger_news_event(self, stock_id: int, impact: float) -> None:
        """触发新闻事件"""
        if stock_id in self.stocks_cache:
            stock = self.stocks_cache[stock_id]
            stock.news_impact = max(-0.1, min(0.1, impact))  # 限制在-10%到10%
            logger.info(f"📰 股票 {stock.code} 触发新闻事件，影响: {impact:.2%}")


# 全局市场模拟器实例
market_simulator = MarketSimulator()
