import React from "react";
import { OTPInput } from "input-otp";
declare const InputOTP: React.FC<React.ComponentPropsWithoutRef<typeof OTPInput>>;
declare const InputOTPGroup: React.FC<Omit<React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const InputOTPSlot: React.FC<Omit<React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    index: number;
    size?: "default" | "lg" | undefined;
} & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const InputOTPSeparator: React.FC<Omit<React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
export { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };
