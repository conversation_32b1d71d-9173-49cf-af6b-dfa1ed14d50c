import { type DialogProps } from "@radix-ui/react-dialog";
import { Command as CommandPrimitive } from "cmdk";
import React from "react";
declare const Command: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive>>;
type CommandDialogProps = {} & DialogProps;
declare const CommandDialog: ({ children, ...props }: CommandDialogProps) => import("react/jsx-runtime").JSX.Element;
declare const CommandInput: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>>;
declare const CommandList: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>>;
declare const CommandEmpty: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>>;
declare const CommandGroup: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>>;
declare const CommandSeparator: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>>;
declare const CommandItem: React.FC<React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>>;
declare const CommandShortcut: {
    ({ className, ...props }: React.HTMLAttributes<HTMLSpanElement>): import("react/jsx-runtime").JSX.Element;
    displayName: string;
};
export { Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut, };
