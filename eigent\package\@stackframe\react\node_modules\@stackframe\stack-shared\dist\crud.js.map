{"version": 3, "sources": ["../src/crud.tsx"], "sourcesContent": ["import * as yup from 'yup';\nimport { yupObject, yupString } from './schema-fields';\nimport { filterUndefined } from './utils/objects';\nimport { NullishCoalesce } from './utils/types';\n\nexport type AccessType = \"client\" | \"server\" | \"admin\";\nexport type CrudOperation = \"create\" | \"read\" | \"update\" | \"delete\";\nexport type CrudlOperation = \"create\" | \"read\" | \"update\" | \"delete\" | \"list\";\nexport type AccessTypeXCrudOperation = `${AccessType}${Capitalize<CrudOperation>}`;\nexport type AccessTypeXCrudlOperation = `${AccessType}${Capitalize<CrudlOperation>}`;\n\ndeclare module 'yup' {\n  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions\n  export interface CustomSchemaMetadata {\n    openapiField?: {\n      description?: string,\n      exampleValue?: any,\n      hidden?: boolean,\n      onlyShowInOperations?: Capitalize<CrudlOperation>[],\n    },\n  }\n}\n\ntype ShownEndpointDocumentation = {\n  summary: string,\n  description: string,\n  tags?: string[],\n  crudOperation?: Capitalize<CrudlOperation>,\n};\nexport type EndpointDocumentation =\n  | ({ hidden: true } & Partial<ShownEndpointDocumentation>)\n  | ({ hidden?: boolean } & ShownEndpointDocumentation);\n\n\ntype InnerCrudSchema<\n  CreateSchema extends yup.AnySchema | undefined = yup.AnySchema | undefined,\n  ReadSchema extends yup.AnySchema | undefined = yup.AnySchema | undefined,\n  UpdateSchema extends yup.AnySchema | undefined = yup.AnySchema | undefined,\n  DeleteSchema extends yup.AnySchema | undefined = yup.AnySchema | undefined,\n> = {\n  createSchema: CreateSchema,\n  createDocs: EndpointDocumentation | undefined,\n\n  readSchema: ReadSchema,\n  readDocs: EndpointDocumentation | undefined,\n  listDocs: EndpointDocumentation | undefined,\n\n  updateSchema: UpdateSchema,\n  updateDocs: EndpointDocumentation | undefined,\n\n  deleteSchema: DeleteSchema,\n  deleteDocs: EndpointDocumentation | undefined,\n};\n\nexport type CrudSchema<\n  ClientSchema extends InnerCrudSchema = InnerCrudSchema,\n  ServerSchema extends InnerCrudSchema = InnerCrudSchema,\n  AdminSchema extends InnerCrudSchema = InnerCrudSchema,\n> = {\n  client: ClientSchema,\n  server: ServerSchema,\n  admin: AdminSchema,\n\n  hasCreate: boolean,\n  hasRead: boolean,\n  hasUpdate: boolean,\n  hasDelete: boolean,\n};\n\nexport type CrudSchemaCreationOptions = {\n  [K in AccessTypeXCrudOperation as `${K}Schema`]?: yup.AnySchema\n};\n\ntype FillInOptionalsPrepareStep<O extends CrudSchemaCreationOptions> =\n  & { [K in keyof Required<CrudSchemaCreationOptions>]: K extends keyof O ? O[K] : undefined };\n\ntype FillInOptionalsStep<O extends FillInOptionalsPrepareStep<CrudSchemaCreationOptions>> = {\n  clientCreateSchema: NullishCoalesce<O['clientCreateSchema'], undefined>,\n  clientReadSchema: NullishCoalesce<O['clientReadSchema'], undefined>,\n  clientUpdateSchema: NullishCoalesce<O['clientUpdateSchema'], undefined>,\n  clientDeleteSchema: NullishCoalesce<O['clientDeleteSchema'], undefined>,\n\n  serverCreateSchema: NullishCoalesce<O['serverCreateSchema'], O['clientCreateSchema']>,\n  serverReadSchema: NullishCoalesce<O['serverReadSchema'], O['clientReadSchema']>,\n  serverUpdateSchema: NullishCoalesce<O['serverUpdateSchema'], O['clientUpdateSchema']>,\n  serverDeleteSchema: NullishCoalesce<O['serverDeleteSchema'], O['clientDeleteSchema']>,\n\n  adminCreateSchema: NullishCoalesce<O['adminCreateSchema'], O['serverCreateSchema']>,\n  adminReadSchema: NullishCoalesce<O['adminReadSchema'], O['serverReadSchema']>,\n  adminUpdateSchema: NullishCoalesce<O['adminUpdateSchema'], O['serverUpdateSchema']>,\n  adminDeleteSchema: NullishCoalesce<O['adminDeleteSchema'], O['serverDeleteSchema']>,\n};\n\ntype FillInOptionals<O extends CrudSchemaCreationOptions> = FillInOptionalsStep<FillInOptionalsStep<FillInOptionalsStep<FillInOptionalsPrepareStep<O>>>>;\n\ntype CrudSchemaFromOptionsInner<O extends FillInOptionals<any>> = CrudSchema<\n  InnerCrudSchema<O['clientCreateSchema'], O['clientReadSchema'], O['clientUpdateSchema'], O['clientDeleteSchema']>,\n  InnerCrudSchema<O['serverCreateSchema'], O['serverReadSchema'], O['serverUpdateSchema'], O['serverDeleteSchema']>,\n  InnerCrudSchema<O['adminCreateSchema'], O['adminReadSchema'], O['adminUpdateSchema'], O['adminDeleteSchema']>\n>;\n\nexport type CrudSchemaFromOptions<O extends CrudSchemaCreationOptions> = CrudSchemaFromOptionsInner<FillInOptionals<O>>;\n\ntype InnerCrudTypeOf<S extends InnerCrudSchema> =\n  & (S['createSchema'] extends {} ? { Create: yup.InferType<S['createSchema']> } : {})\n  & (S['readSchema'] extends {} ? { Read: yup.InferType<S['readSchema']> } : {})\n  & (S['updateSchema'] extends {} ? { Update: yup.InferType<S['updateSchema']> } : {})\n  & (S['deleteSchema'] extends {} ? { Delete: yup.InferType<S['deleteSchema']> } : {})\n  & (S['readSchema'] extends {} ? { List: {\n    items: yup.InferType<S['readSchema']>[],\n    is_paginated: boolean,\n    pagination?: {\n      next_cursor: string | null,\n    },\n  }, } : {});\n\nexport type CrudTypeOf<S extends CrudSchema> = {\n  Client: InnerCrudTypeOf<S['client']>,\n  Server: InnerCrudTypeOf<S['server']>,\n  Admin: InnerCrudTypeOf<S['admin']>,\n}\n\ntype CrudDocsCreationOptions<SO extends CrudSchemaCreationOptions> = {\n  [X in AccessTypeXCrudlOperation]?: EndpointDocumentation\n};\n\nexport function createCrud<SO extends CrudSchemaCreationOptions>(options: SO & { docs?: CrudDocsCreationOptions<SO> }): CrudSchemaFromOptions<SO> {\n  const docs = options.docs ?? {};\n  const client = {\n    createSchema: options.clientCreateSchema,\n    createDocs: docs.clientCreate,\n\n    readSchema: options.clientReadSchema,\n    readDocs: docs.clientRead,\n    listDocs: docs.clientList,\n\n    updateSchema: options.clientUpdateSchema,\n    updateDocs: docs.clientUpdate,\n\n    deleteSchema: options.clientDeleteSchema,\n    deleteDocs: docs.clientDelete,\n  };\n\n  const serverOverrides = filterUndefined({\n    createSchema: options.serverCreateSchema,\n    createDocs: docs.serverCreate,\n\n    readSchema: options.serverReadSchema,\n    readDocs: docs.serverRead,\n    listDocs: docs.serverList,\n\n    updateSchema: options.serverUpdateSchema,\n    updateDocs: docs.serverUpdate,\n\n    deleteSchema: options.serverDeleteSchema,\n    deleteDocs: docs.serverDelete,\n  });\n  const server = {\n    ...client,\n    ...serverOverrides,\n  };\n\n  const adminOverrides = filterUndefined({\n    createSchema: options.adminCreateSchema,\n    createDocs: docs.adminCreate,\n\n    readSchema: options.adminReadSchema,\n    readDocs: docs.adminRead,\n    listDocs: docs.adminList,\n\n    updateSchema: options.adminUpdateSchema,\n    updateDocs: docs.adminUpdate,\n\n    deleteSchema: options.adminDeleteSchema,\n    deleteDocs: docs.adminDelete,\n  });\n  const admin = {\n    ...server,\n    ...adminOverrides,\n  };\n\n  return {\n    client: client as any,\n    server: server as any,\n    admin: admin as any,\n\n    hasCreate: !!admin.createSchema,\n    hasRead: !!admin.readSchema,\n    hasUpdate: !!admin.updateSchema,\n    hasDelete: !!admin.deleteSchema,\n  };\n}\n\nundefined?.test(\"createCrud\", ({ expect }) => {\n  // Test with empty options\n  const emptyCrud = createCrud({});\n  expect(emptyCrud.hasCreate).toBe(false);\n  expect(emptyCrud.hasRead).toBe(false);\n  expect(emptyCrud.hasUpdate).toBe(false);\n  expect(emptyCrud.hasDelete).toBe(false);\n  expect(emptyCrud.client.createSchema).toBeUndefined();\n  expect(emptyCrud.server.createSchema).toBeUndefined();\n  expect(emptyCrud.admin.createSchema).toBeUndefined();\n\n  // Test with client schemas only\n  const mockSchema = yupObject().shape({\n    name: yupString().defined(),\n  });\n\n  const clientOnlyCrud = createCrud({\n    clientCreateSchema: mockSchema,\n    clientReadSchema: mockSchema,\n  });\n  expect(clientOnlyCrud.hasCreate).toBe(true);\n  expect(clientOnlyCrud.hasRead).toBe(true);\n  expect(clientOnlyCrud.hasUpdate).toBe(false);\n  expect(clientOnlyCrud.hasDelete).toBe(false);\n  expect(clientOnlyCrud.client.createSchema).toBe(mockSchema);\n  expect(clientOnlyCrud.server.createSchema).toBe(mockSchema);\n  expect(clientOnlyCrud.admin.createSchema).toBe(mockSchema);\n\n  // Test with server overrides\n  const serverSchema = yupObject().shape({\n    name: yupString().defined(),\n    internalField: yupString().defined(),\n  });\n\n  const serverOverrideCrud = createCrud({\n    clientCreateSchema: mockSchema,\n    serverCreateSchema: serverSchema,\n  });\n  expect(serverOverrideCrud.hasCreate).toBe(true);\n  expect(serverOverrideCrud.client.createSchema).toBe(mockSchema);\n  expect(serverOverrideCrud.server.createSchema).toBe(serverSchema);\n  expect(serverOverrideCrud.admin.createSchema).toBe(serverSchema);\n\n  // Test with admin overrides\n  const adminSchema = yupObject().shape({\n    name: yupString().defined(),\n    internalField: yupString().defined(),\n    adminField: yupString().defined(),\n  });\n\n  const fullOverrideCrud = createCrud({\n    clientCreateSchema: mockSchema,\n    serverCreateSchema: serverSchema,\n    adminCreateSchema: adminSchema,\n  });\n  expect(fullOverrideCrud.hasCreate).toBe(true);\n  expect(fullOverrideCrud.client.createSchema).toBe(mockSchema);\n  expect(fullOverrideCrud.server.createSchema).toBe(serverSchema);\n  expect(fullOverrideCrud.admin.createSchema).toBe(adminSchema);\n\n  // Test with documentation\n  const crudWithDocs = createCrud({\n    clientCreateSchema: mockSchema,\n    docs: {\n      clientCreate: {\n        summary: \"Create a resource\",\n        description: \"Creates a new resource\",\n        tags: [\"resources\"],\n      },\n    },\n  });\n  expect(crudWithDocs.client.createDocs).toEqual({\n    summary: \"Create a resource\",\n    description: \"Creates a new resource\",\n    tags: [\"resources\"],\n  });\n  expect(crudWithDocs.server.createDocs).toEqual({\n    summary: \"Create a resource\",\n    description: \"Creates a new resource\",\n    tags: [\"resources\"],\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,2BAAqC;AACrC,qBAAgC;AA4HzB,SAAS,WAAiD,SAAiF;AAChJ,QAAM,OAAO,QAAQ,QAAQ,CAAC;AAC9B,QAAM,SAAS;AAAA,IACb,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,IAEjB,YAAY,QAAQ;AAAA,IACpB,UAAU,KAAK;AAAA,IACf,UAAU,KAAK;AAAA,IAEf,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,IAEjB,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,EACnB;AAEA,QAAM,sBAAkB,gCAAgB;AAAA,IACtC,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,IAEjB,YAAY,QAAQ;AAAA,IACpB,UAAU,KAAK;AAAA,IACf,UAAU,KAAK;AAAA,IAEf,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,IAEjB,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,EACnB,CAAC;AACD,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,QAAM,qBAAiB,gCAAgB;AAAA,IACrC,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,IAEjB,YAAY,QAAQ;AAAA,IACpB,UAAU,KAAK;AAAA,IACf,UAAU,KAAK;AAAA,IAEf,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,IAEjB,cAAc,QAAQ;AAAA,IACtB,YAAY,KAAK;AAAA,EACnB,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IAEA,WAAW,CAAC,CAAC,MAAM;AAAA,IACnB,SAAS,CAAC,CAAC,MAAM;AAAA,IACjB,WAAW,CAAC,CAAC,MAAM;AAAA,IACnB,WAAW,CAAC,CAAC,MAAM;AAAA,EACrB;AACF;", "names": []}