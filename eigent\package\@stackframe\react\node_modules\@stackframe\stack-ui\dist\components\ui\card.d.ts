declare const Card: import("react").FC<import("react").HTMLAttributes<HTMLDivElement> & {
    ref?: import("react").Ref<HTMLDivElement> | undefined;
}>;
declare const CardHeader: import("react").FC<import("react").HTMLAttributes<HTMLDivElement> & {
    ref?: import("react").Ref<HTMLDivElement> | undefined;
}>;
declare const CardTitle: import("react").FC<import("react").HTMLAttributes<HTMLHeadingElement> & {
    ref?: import("react").Ref<HTMLParagraphElement> | undefined;
}>;
declare const CardDescription: import("react").FC<import("react").HTMLAttributes<HTMLParagraphElement> & {
    ref?: import("react").Ref<HTMLParagraphElement> | undefined;
}>;
declare const CardContent: import("react").FC<import("react").HTMLAttributes<HTMLDivElement> & {
    ref?: import("react").Ref<HTMLDivElement> | undefined;
}>;
declare const CardSubtitle: import("react").FC<import("react").HTMLAttributes<HTMLParagraphElement> & {
    ref?: import("react").Ref<HTMLParagraphElement> | undefined;
}>;
declare const CardFooter: import("react").FC<import("react").HTMLAttributes<HTMLDivElement> & {
    ref?: import("react").Ref<HTMLDivElement> | undefined;
}>;
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardSubtitle, CardTitle };
