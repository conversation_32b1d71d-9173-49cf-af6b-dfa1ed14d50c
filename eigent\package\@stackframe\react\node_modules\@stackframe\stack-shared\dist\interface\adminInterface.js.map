{"version": 3, "sources": ["../../src/interface/adminInterface.ts"], "sourcesContent": ["import { InternalSession } from \"../sessions\";\nimport { EmailTemplateCrud, EmailTemplateType } from \"./crud/email-templates\";\nimport { InternalEmailsCrud } from \"./crud/emails\";\nimport { InternalApiKeysCrud } from \"./crud/internal-api-keys\";\nimport { ProjectPermissionDefinitionsCrud } from \"./crud/project-permissions\";\nimport { ProjectsCrud } from \"./crud/projects\";\nimport { SvixTokenCrud } from \"./crud/svix-token\";\nimport { TeamPermissionDefinitionsCrud } from \"./crud/team-permissions\";\nimport { ServerAuthApplicationOptions, StackServerInterface } from \"./serverInterface\";\n\nexport type AdminAuthApplicationOptions = ServerAuthApplicationOptions &(\n  | {\n    superSecretAdminKey: string,\n  }\n  | {\n    projectOwnerSession: InternalSession,\n  }\n);\n\nexport type InternalApiKeyCreateCrudRequest = {\n  has_publishable_client_key: boolean,\n  has_secret_server_key: boolean,\n  has_super_secret_admin_key: boolean,\n  expires_at_millis: number,\n  description: string,\n};\n\nexport type InternalApiKeyCreateCrudResponse = InternalApiKeysCrud[\"Admin\"][\"Read\"] & {\n  publishable_client_key?: string,\n  secret_server_key?: string,\n  super_secret_admin_key?: string,\n};\n\nexport class StackAdminInterface extends StackServerInterface {\n  constructor(public readonly options: AdminAuthApplicationOptions) {\n    super(options);\n  }\n\n  public async sendAdminRequest(path: string, options: RequestInit, session: InternalSession | null, requestType: \"admin\" = \"admin\") {\n    return await this.sendServerRequest(\n      path,\n      {\n        ...options,\n        headers: {\n          \"x-stack-super-secret-admin-key\": \"superSecretAdminKey\" in this.options ? this.options.superSecretAdminKey : \"\",\n          ...options.headers,\n        },\n      },\n      session,\n      requestType,\n    );\n  }\n\n  async getProject(): Promise<ProjectsCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(\n      \"/internal/projects/current\",\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateProject(update: ProjectsCrud[\"Admin\"][\"Update\"]): Promise<ProjectsCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(\n      \"/internal/projects/current\",\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(update),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async createInternalApiKey(\n    options: InternalApiKeyCreateCrudRequest,\n  ): Promise<InternalApiKeyCreateCrudResponse> {\n    const response = await this.sendAdminRequest(\n      \"/internal/api-keys\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(options),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async listInternalApiKeys(): Promise<InternalApiKeysCrud[\"Admin\"][\"Read\"][]> {\n    const response = await this.sendAdminRequest(\"/internal/api-keys\", {}, null);\n    const result = await response.json() as InternalApiKeysCrud[\"Admin\"][\"List\"];\n    return result.items;\n  }\n\n  async revokeInternalApiKeyById(id: string) {\n    await this.sendAdminRequest(\n      `/internal/api-keys/${id}`, {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          revoked: true,\n        }),\n      },\n      null,\n    );\n  }\n\n  async getInternalApiKey(id: string, session: InternalSession): Promise<InternalApiKeysCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(`/internal/api-keys/${id}`, {}, session);\n    return await response.json();\n  }\n\n  async listEmailTemplates(): Promise<EmailTemplateCrud['Admin']['Read'][]> {\n    const response = await this.sendAdminRequest(`/email-templates`, {}, null);\n    const result = await response.json() as EmailTemplateCrud['Admin']['List'];\n    return result.items;\n  }\n\n  async updateEmailTemplate(type: EmailTemplateType, data: EmailTemplateCrud['Admin']['Update']): Promise<EmailTemplateCrud['Admin']['Read']> {\n    const result = await this.sendAdminRequest(\n      `/email-templates/${type}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await result.json();\n  }\n\n  async resetEmailTemplate(type: EmailTemplateType): Promise<void> {\n    await this.sendAdminRequest(\n      `/email-templates/${type}`,\n      { method: \"DELETE\" },\n      null\n    );\n  }\n\n  // Team permission definitions methods\n  async listTeamPermissionDefinitions(): Promise<TeamPermissionDefinitionsCrud['Admin']['Read'][]> {\n    const response = await this.sendAdminRequest(`/team-permission-definitions`, {}, null);\n    const result = await response.json() as TeamPermissionDefinitionsCrud['Admin']['List'];\n    return result.items;\n  }\n\n  async createTeamPermissionDefinition(data: TeamPermissionDefinitionsCrud['Admin']['Create']): Promise<TeamPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      \"/team-permission-definitions\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateTeamPermissionDefinition(permissionId: string, data: TeamPermissionDefinitionsCrud['Admin']['Update']): Promise<TeamPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      `/team-permission-definitions/${permissionId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteTeamPermissionDefinition(permissionId: string): Promise<void> {\n    await this.sendAdminRequest(\n      `/team-permission-definitions/${permissionId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async listProjectPermissionDefinitions(): Promise<ProjectPermissionDefinitionsCrud['Admin']['Read'][]> {\n    const response = await this.sendAdminRequest(`/project-permission-definitions`, {}, null);\n    const result = await response.json() as ProjectPermissionDefinitionsCrud['Admin']['List'];\n    return result.items;\n  }\n\n  async createProjectPermissionDefinition(data: ProjectPermissionDefinitionsCrud['Admin']['Create']): Promise<ProjectPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      \"/project-permission-definitions\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async updateProjectPermissionDefinition(permissionId: string, data: ProjectPermissionDefinitionsCrud['Admin']['Update']): Promise<ProjectPermissionDefinitionsCrud['Admin']['Read']> {\n    const response = await this.sendAdminRequest(\n      `/project-permission-definitions/${permissionId}`,\n      {\n        method: \"PATCH\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteProjectPermissionDefinition(permissionId: string): Promise<void> {\n    await this.sendAdminRequest(\n      `/project-permission-definitions/${permissionId}`,\n      { method: \"DELETE\" },\n      null,\n    );\n  }\n\n  async getSvixToken(): Promise<SvixTokenCrud[\"Admin\"][\"Read\"]> {\n    const response = await this.sendAdminRequest(\n      \"/webhooks/svix-token\",\n      {\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n        },\n        body: JSON.stringify({}),\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async deleteProject(): Promise<void> {\n    await this.sendAdminRequest(\n      \"/internal/projects/current\",\n      {\n        method: \"DELETE\",\n      },\n      null,\n    );\n  }\n\n  async getMetrics(): Promise<any> {\n    const response = await this.sendAdminRequest(\n      \"/internal/metrics\",\n      {\n        method: \"GET\",\n      },\n      null,\n    );\n    return await response.json();\n  }\n\n  async sendTestEmail(data: {\n    recipient_email: string,\n    email_config: {\n      host: string,\n      port: number,\n      username: string,\n      password: string,\n      sender_email: string,\n      sender_name: string,\n    },\n  }): Promise<{ success: boolean, error_message?: string }> {\n    const response = await this.sendAdminRequest(`/internal/send-test-email`, {\n      method: \"POST\",\n      headers: {\n        \"content-type\": \"application/json\",\n      },\n      body: JSON.stringify(data),\n    }, null);\n    return await response.json();\n  }\n\n  async listSentEmails(): Promise<InternalEmailsCrud[\"Admin\"][\"List\"]> {\n    const response = await this.sendAdminRequest(\"/internal/emails\", {\n      method: \"GET\",\n    }, null);\n    return await response.json();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,6BAAmE;AAyB5D,IAAM,sBAAN,cAAkC,4CAAqB;AAAA,EAC5D,YAA4B,SAAsC;AAChE,UAAM,OAAO;AADa;AAAA,EAE5B;AAAA,EAEA,MAAa,iBAAiB,MAAc,SAAsB,SAAiC,cAAuB,SAAS;AACjI,WAAO,MAAM,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,QACE,GAAG;AAAA,QACH,SAAS;AAAA,UACP,kCAAkC,yBAAyB,KAAK,UAAU,KAAK,QAAQ,sBAAsB;AAAA,UAC7G,GAAG,QAAQ;AAAA,QACb;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,aAAqD;AACzD,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,cAAc,QAAiF;AACnG,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,qBACJ,SAC2C;AAC3C,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,OAAO;AAAA,MAC9B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,sBAAuE;AAC3E,UAAM,WAAW,MAAM,KAAK,iBAAiB,sBAAsB,CAAC,GAAG,IAAI;AAC3E,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,yBAAyB,IAAY;AACzC,UAAM,KAAK;AAAA,MACT,sBAAsB,EAAE;AAAA,MAAI;AAAA,QAC1B,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,IAAY,SAAyE;AAC3G,UAAM,WAAW,MAAM,KAAK,iBAAiB,sBAAsB,EAAE,IAAI,CAAC,GAAG,OAAO;AACpF,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,qBAAoE;AACxE,UAAM,WAAW,MAAM,KAAK,iBAAiB,oBAAoB,CAAC,GAAG,IAAI;AACzE,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,oBAAoB,MAAyB,MAAyF;AAC1I,UAAM,SAAS,MAAM,KAAK;AAAA,MACxB,oBAAoB,IAAI;AAAA,MACxB;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,OAAO,KAAK;AAAA,EAC3B;AAAA,EAEA,MAAM,mBAAmB,MAAwC;AAC/D,UAAM,KAAK;AAAA,MACT,oBAAoB,IAAI;AAAA,MACxB,EAAE,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,gCAA2F;AAC/F,UAAM,WAAW,MAAM,KAAK,iBAAiB,gCAAgC,CAAC,GAAG,IAAI;AACrF,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,+BAA+B,MAAiH;AACpJ,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,+BAA+B,cAAsB,MAAiH;AAC1K,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,gCAAgC,YAAY;AAAA,MAC5C;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,+BAA+B,cAAqC;AACxE,UAAM,KAAK;AAAA,MACT,gCAAgC,YAAY;AAAA,MAC5C,EAAE,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,mCAAiG;AACrG,UAAM,WAAW,MAAM,KAAK,iBAAiB,mCAAmC,CAAC,GAAG,IAAI;AACxF,UAAM,SAAS,MAAM,SAAS,KAAK;AACnC,WAAO,OAAO;AAAA,EAChB;AAAA,EAEA,MAAM,kCAAkC,MAAuH;AAC7J,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,kCAAkC,cAAsB,MAAuH;AACnL,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B,mCAAmC,YAAY;AAAA,MAC/C;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,IAAI;AAAA,MAC3B;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,kCAAkC,cAAqC;AAC3E,UAAM,KAAK;AAAA,MACT,mCAAmC,YAAY;AAAA,MAC/C,EAAE,QAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,eAAwD;AAC5D,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,gBAA+B;AACnC,UAAM,KAAK;AAAA,MACT;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,aAA2B;AAC/B,UAAM,WAAW,MAAM,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,cAAc,MAUsC;AACxD,UAAM,WAAW,MAAM,KAAK,iBAAiB,6BAA6B;AAAA,MACxE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B,GAAG,IAAI;AACP,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEA,MAAM,iBAA+D;AACnE,UAAM,WAAW,MAAM,KAAK,iBAAiB,oBAAoB;AAAA,MAC/D,QAAQ;AAAA,IACV,GAAG,IAAI;AACP,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AACF;", "names": []}