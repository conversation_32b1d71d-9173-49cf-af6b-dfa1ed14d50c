{"version": 3, "sources": ["../../../../src/interface/crud/team-permissions.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport * as schemaFields from \"../../schema-fields\";\nimport { yupMixed, yupObject } from \"../../schema-fields\";\nimport { WebhookEvent } from \"../webhooks\";\n\n// =============== Team permissions =================\n\nexport const teamPermissionsCrudClientReadSchema = yupObject({\n  id: schemaFields.permissionDefinitionIdSchema.defined(),\n  user_id: schemaFields.userIdSchema.defined(),\n  team_id: schemaFields.teamIdSchema.defined(),\n}).defined();\n\nexport const teamPermissionsCrudServerCreateSchema = yupObject({\n}).defined();\n\nexport const teamPermissionsCrudServerDeleteSchema = yupMixed();\n\nexport const teamPermissionsCrud = createCrud({\n  clientReadSchema: teamPermissionsCrudClientReadSchema,\n  serverCreateSchema: teamPermissionsCrudServerCreateSchema,\n  serverDeleteSchema: teamPermissionsCrudServerDeleteSchema,\n  docs: {\n    clientList: {\n      summary: \"List team permissions\",\n      description: \"List team permissions of the current user. `user_id=me` must be set for client requests. Note that this might contain the permissions with the same permission ID across different teams. `(team_id, user_id, permission_id)` together uniquely identify a permission.\",\n      tags: [\"Permissions\"],\n    },\n    serverList: {\n      summary: \"List team permissions of a user\",\n      description: \"Query and filter the permission with `team_id`, `user_id`, and `permission_id`. Note that this might contain the permissions with the same permission ID across different teams and users. `(team_id, user_id, permission_id)` together uniquely identify a permission.\",\n      tags: [\"Permissions\"],\n    },\n    serverCreate: {\n      summary: \"Grant a team permission to a user\",\n      description: \"Grant a team permission to a user (the team permission must be created first on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    serverDelete: {\n      summary: \"Revoke a team permission from a user\",\n      description: \"Revoke a team permission from a user\",\n      tags: [\"Permissions\"],\n    },\n  },\n});\nexport type TeamPermissionsCrud = CrudTypeOf<typeof teamPermissionsCrud>;\n\nexport const teamPermissionCreatedWebhookEvent = {\n  type: \"team_permission.created\",\n  schema: teamPermissionsCrud.server.readSchema,\n  metadata: {\n    summary: \"Team Permission Created\",\n    description: \"This event is triggered when a team permission is created.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof teamPermissionsCrud.server.readSchema>;\n\nexport const teamPermissionDeletedWebhookEvent = {\n  type: \"team_permission.deleted\",\n  schema: teamPermissionsCrud.server.readSchema,\n  metadata: {\n    summary: \"Team Permission Deleted\",\n    description: \"This event is triggered when a team permission is deleted.\",\n    tags: [\"Teams\"],\n  },\n} satisfies WebhookEvent<typeof teamPermissionsCrud.server.readSchema>;\n\n// =============== Team permission definitions =================\n\nexport const teamPermissionDefinitionsCrudAdminReadSchema = yupObject({\n  id: schemaFields.permissionDefinitionIdSchema.defined(),\n  description: schemaFields.teamPermissionDescriptionSchema.optional(),\n  contained_permission_ids: schemaFields.containedPermissionIdsSchema.defined(),\n}).defined();\n\nexport const teamPermissionDefinitionsCrudAdminCreateSchema = yupObject({\n  id: schemaFields.customPermissionDefinitionIdSchema.defined(),\n  description: schemaFields.teamPermissionDescriptionSchema.optional(),\n  contained_permission_ids: schemaFields.containedPermissionIdsSchema.optional(),\n}).defined();\n\nexport const teamPermissionDefinitionsCrudAdminUpdateSchema = yupObject({\n  id: schemaFields.customPermissionDefinitionIdSchema.optional(),\n  description: schemaFields.teamPermissionDescriptionSchema.optional(),\n  contained_permission_ids: schemaFields.containedPermissionIdsSchema.optional(),\n}).defined();\n\nexport const teamPermissionDefinitionsCrudAdminDeleteSchema = yupMixed();\n\nexport const teamPermissionDefinitionsCrud = createCrud({\n  adminReadSchema: teamPermissionDefinitionsCrudAdminReadSchema,\n  adminCreateSchema: teamPermissionDefinitionsCrudAdminCreateSchema,\n  adminUpdateSchema: teamPermissionDefinitionsCrudAdminUpdateSchema,\n  adminDeleteSchema: teamPermissionDefinitionsCrudAdminDeleteSchema,\n  docs: {\n    adminList: {\n      summary: \"List team permission definitions\",\n      description: \"Query and filter the permission with team_id, user_id, and permission_id (the equivalent of listing permissions on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    adminCreate: {\n      summary: \"Create a new team permission definition\",\n      description: \"Create a new permission definition (the equivalent of creating a new permission on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    adminUpdate: {\n      summary: \"Update a team permission definition\",\n      description: \"Update a permission definition (the equivalent of updating a permission on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n    adminDelete: {\n      summary: \"Delete a team permission definition\",\n      description: \"Delete a permission definition (the equivalent of deleting a permission on the Stack dashboard)\",\n      tags: [\"Permissions\"],\n    },\n  },\n});\n\nexport type TeamPermissionDefinitionsCrud = CrudTypeOf<typeof teamPermissionDefinitionsCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,YAAY,kBAAkB;AAC9B,SAAS,UAAU,iBAAiB;AAK7B,IAAM,sCAAsC,UAAU;AAAA,EAC3D,IAAiB,0CAA6B,QAAQ;AAAA,EACtD,SAAsB,0BAAa,QAAQ;AAAA,EAC3C,SAAsB,0BAAa,QAAQ;AAC7C,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,UAAU,CAC/D,CAAC,EAAE,QAAQ;AAEJ,IAAM,wCAAwC,SAAS;AAEvD,IAAM,sBAAsB,WAAW;AAAA,EAC5C,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,MAAM;AAAA,IACJ,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,EACF;AACF,CAAC;AAGM,IAAM,oCAAoC;AAAA,EAC/C,MAAM;AAAA,EACN,QAAQ,oBAAoB,OAAO;AAAA,EACnC,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAEO,IAAM,oCAAoC;AAAA,EAC/C,MAAM;AAAA,EACN,QAAQ,oBAAoB,OAAO;AAAA,EACnC,UAAU;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,MAAM,CAAC,OAAO;AAAA,EAChB;AACF;AAIO,IAAM,+CAA+C,UAAU;AAAA,EACpE,IAAiB,0CAA6B,QAAQ;AAAA,EACtD,aAA0B,6CAAgC,SAAS;AAAA,EACnE,0BAAuC,0CAA6B,QAAQ;AAC9E,CAAC,EAAE,QAAQ;AAEJ,IAAM,iDAAiD,UAAU;AAAA,EACtE,IAAiB,gDAAmC,QAAQ;AAAA,EAC5D,aAA0B,6CAAgC,SAAS;AAAA,EACnE,0BAAuC,0CAA6B,SAAS;AAC/E,CAAC,EAAE,QAAQ;AAEJ,IAAM,iDAAiD,UAAU;AAAA,EACtE,IAAiB,gDAAmC,SAAS;AAAA,EAC7D,aAA0B,6CAAgC,SAAS;AAAA,EACnE,0BAAuC,0CAA6B,SAAS;AAC/E,CAAC,EAAE,QAAQ;AAEJ,IAAM,iDAAiD,SAAS;AAEhE,IAAM,gCAAgC,WAAW;AAAA,EACtD,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,aAAa;AAAA,MACb,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,EACF;AACF,CAAC;", "names": []}