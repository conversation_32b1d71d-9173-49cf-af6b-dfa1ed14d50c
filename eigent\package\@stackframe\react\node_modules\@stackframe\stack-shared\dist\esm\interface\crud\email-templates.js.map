{"version": 3, "sources": ["../../../../src/interface/crud/email-templates.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { jsonSchema, yupBoolean, yupMixed, yupObject, yupString } from \"../../schema-fields\";\n\nexport type EmailTemplateType = typeof emailTemplateTypes[number];\nexport const emailTemplateTypes = ['email_verification', 'password_reset', 'magic_link', 'team_invitation'] as const;\n\nexport const emailTemplateAdminReadSchema = yupObject({\n  type: yupString().oneOf(emailTemplateTypes).defined(),\n  subject: yupString().defined(),\n  content: jsonSchema.defined(),\n  is_default: yupBoolean().defined(),\n}).defined();\n\nexport const emailTemplateCrudAdminUpdateSchema = yupObject({\n  content: jsonSchema.nonNullable().optional(),\n  subject: yupString().optional(),\n}).defined();\n\nexport const emailTemplateCrudAdminDeleteSchema = yupMixed();\n\nexport const emailTemplateCrudAdminCreateSchema = yupObject({\n  type: yupString().oneOf(emailTemplateTypes).defined(),\n  content: jsonSchema.defined(),\n  subject: yupString().defined(),\n}).defined();\n\nexport const emailTemplateCrud = createCrud({\n  adminReadSchema: emailTemplateAdminReadSchema,\n  adminUpdateSchema: emailTemplateCrudAdminUpdateSchema,\n  adminCreateSchema: emailTemplateCrudAdminCreateSchema,\n  adminDeleteSchema: emailTemplateCrudAdminDeleteSchema,\n  docs: {\n    adminRead: {\n      hidden: true,\n    },\n    adminCreate: {\n      hidden: true,\n    },\n    adminUpdate: {\n      hidden: true,\n    },\n    adminDelete: {\n      hidden: true,\n    },\n    adminList: {\n      hidden: true,\n    }\n  }\n});\nexport type EmailTemplateCrud = CrudTypeOf<typeof emailTemplateCrud>;\n"], "mappings": ";AAAA,SAAqB,kBAAkB;AACvC,SAAS,YAAY,YAAY,UAAU,WAAW,iBAAiB;AAGhE,IAAM,qBAAqB,CAAC,sBAAsB,kBAAkB,cAAc,iBAAiB;AAEnG,IAAM,+BAA+B,UAAU;AAAA,EACpD,MAAM,UAAU,EAAE,MAAM,kBAAkB,EAAE,QAAQ;AAAA,EACpD,SAAS,UAAU,EAAE,QAAQ;AAAA,EAC7B,SAAS,WAAW,QAAQ;AAAA,EAC5B,YAAY,WAAW,EAAE,QAAQ;AACnC,CAAC,EAAE,QAAQ;AAEJ,IAAM,qCAAqC,UAAU;AAAA,EAC1D,SAAS,WAAW,YAAY,EAAE,SAAS;AAAA,EAC3C,SAAS,UAAU,EAAE,SAAS;AAChC,CAAC,EAAE,QAAQ;AAEJ,IAAM,qCAAqC,SAAS;AAEpD,IAAM,qCAAqC,UAAU;AAAA,EAC1D,MAAM,UAAU,EAAE,MAAM,kBAAkB,EAAE,QAAQ;AAAA,EACpD,SAAS,WAAW,QAAQ;AAAA,EAC5B,SAAS,UAAU,EAAE,QAAQ;AAC/B,CAAC,EAAE,QAAQ;AAEJ,IAAM,oBAAoB,WAAW;AAAA,EAC1C,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}