{"version": 3, "sources": ["../../../src/utils/errors.tsx"], "sourcesContent": ["import { globalVar } from \"./globals\";\nimport { J<PERSON> } from \"./json\";\nimport { pick } from \"./objects\";\nimport { nicify } from \"./strings\";\n\n\nexport function throwErr(errorMessage: string, extraData?: any): never;\nexport function throwErr(error: Error): never;\nexport function throwErr(...args: StatusErrorConstructorParameters): never;\nexport function throwErr(...args: any[]): never {\n  if (typeof args[0] === \"string\") {\n    throw new StackAssertionError(args[0], args[1]);\n  } else if (args[0] instanceof Error) {\n    throw args[0];\n  } else {\n    // @ts-expect-error\n    throw new StatusError(...args);\n  }\n}\n\nfunction removeStacktraceNameLine(stack: string): string {\n  // some browsers (eg. Chrome) prepend the stack with an extra line with the error name\n  const addsNameLine = new Error().stack?.startsWith(\"Error\\n\");\n  return stack.split(\"\\n\").slice(addsNameLine ? 1 : 0).join(\"\\n\");\n}\n\n\n/**\n * Concatenates the (original) stacktraces of the given errors onto the first.\n *\n * Useful when you invoke an async function to receive a promise without awaiting it immediately. Browsers are smart\n * enough to keep track of the call stack in async function calls when you invoke `.then` within the same async tick,\n * but if you don't, the stacktrace will be lost.\n *\n * Here's an example of the unwanted behavior:\n *\n * ```tsx\n * async function log() {\n *   await wait(0);  // simulate an put the task on the event loop\n *   console.log(new Error().stack);\n * }\n *\n * async function main() {\n *   await log();  // good; prints both \"log\" and \"main\" on the stacktrace\n *   log();  // bad; prints only \"log\" on the stacktrace\n * }\n * ```\n */\nexport function concatStacktraces(first: Error, ...errors: Error[]): void {\n  // some browsers (eg. Firefox) add an extra empty line at the end\n  const addsEmptyLineAtEnd = first.stack?.endsWith(\"\\n\");\n\n\n  // Add a reference to this function itself so that we know that stacktraces were concatenated\n  // If you are coming here from a stacktrace, please know that the two parts before and after this line are different\n  // stacktraces that were concatenated with concatStacktraces\n  const separator = removeStacktraceNameLine(new Error().stack ?? \"\").split(\"\\n\")[0];\n\n\n  for (const error of errors) {\n    const toAppend = removeStacktraceNameLine(error.stack ?? \"\");\n    first.stack += (addsEmptyLineAtEnd ? \"\" : \"\\n\") + separator + \"\\n\" + toAppend;\n  }\n}\n\n\nexport class StackAssertionError extends Error {\n  constructor(message: string, public readonly extraData?: Record<string, any> & ErrorOptions) {\n    const disclaimer = `\\n\\nThis is likely an error in Stack. Please make sure you are running the newest version and report it.`;\n    super(`${message}${message.endsWith(disclaimer) ? \"\" : disclaimer}`, pick(extraData ?? {}, [\"cause\"]));\n\n    Object.defineProperty(this, \"customCaptureExtraArgs\", {\n      get() {\n        return [this.extraData];\n      },\n      enumerable: false,\n    });\n  }\n}\nStackAssertionError.prototype.name = \"StackAssertionError\";\n\n\nexport function errorToNiceString(error: unknown): string {\n  if (!(error instanceof Error)) return `${typeof error}<${nicify(error)}>`;\n  return nicify(error, { maxDepth: 8 });\n}\n\n\nconst errorSinks = new Set<(location: string, error: unknown, ...extraArgs: unknown[]) => void>();\nexport function registerErrorSink(sink: (location: string, error: unknown) => void): void {\n  if (errorSinks.has(sink)) {\n    return;\n  }\n  errorSinks.add(sink);\n}\nregisterErrorSink((location, error, ...extraArgs) => {\n  console.error(\n    `\\x1b[41mCaptured error in ${location}:`,\n    // HACK: Log a nicified version of the error to get around buggy Next.js pretty-printing\n    // https://www.reddit.com/r/nextjs/comments/1gkxdqe/comment/m19kxgn/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button\n    errorToNiceString(error),\n    ...extraArgs,\n    \"\\x1b[0m\",\n  );\n});\nregisterErrorSink((location, error, ...extraArgs) => {\n  globalVar.stackCapturedErrors = globalVar.stackCapturedErrors ?? [];\n  globalVar.stackCapturedErrors.push({ location, error, extraArgs });\n});\n\nexport function captureError(location: string, error: unknown): void {\n  for (const sink of errorSinks) {\n    sink(\n      location,\n      error,\n      ...error && (typeof error === 'object' || typeof error === 'function') && \"customCaptureExtraArgs\" in error && Array.isArray(error.customCaptureExtraArgs) ? (error.customCaptureExtraArgs as any[]) : [],\n    );\n  }\n}\n\n\ntype Status = {\n  statusCode: number,\n  message: string,\n};\n\ntype StatusErrorConstructorParameters =\n| [\n  status: Status,\n  message?: string\n]\n| [\n  statusCode: number | Status,\n  message: string,\n];\n\nexport class StatusError extends Error {\n  private readonly __stackStatusErrorBrand = \"stack-status-error-brand-sentinel\" as const;\n  public name = \"StatusError\";\n  public readonly statusCode: number;\n\n  public static BadRequest = { statusCode: 400, message: \"Bad Request\" };\n  public static Unauthorized = { statusCode: 401, message: \"Unauthorized\" };\n  public static PaymentRequired = { statusCode: 402, message: \"Payment Required\" };\n  public static Forbidden = { statusCode: 403, message: \"Forbidden\" };\n  public static NotFound = { statusCode: 404, message: \"Not Found\" };\n  public static MethodNotAllowed = { statusCode: 405, message: \"Method Not Allowed\" };\n  public static NotAcceptable = { statusCode: 406, message: \"Not Acceptable\" };\n  public static ProxyAuthenticationRequired = { statusCode: 407, message: \"Proxy Authentication Required\" };\n  public static RequestTimeout = { statusCode: 408, message: \"Request Timeout\" };\n  public static Conflict = { statusCode: 409, message: \"Conflict\" };\n  public static Gone = { statusCode: 410, message: \"Gone\" };\n  public static LengthRequired = { statusCode: 411, message: \"Length Required\" };\n  public static PreconditionFailed = { statusCode: 412, message: \"Precondition Failed\" };\n  public static PayloadTooLarge = { statusCode: 413, message: \"Payload Too Large\" };\n  public static URITooLong = { statusCode: 414, message: \"URI Too Long\" };\n  public static UnsupportedMediaType = { statusCode: 415, message: \"Unsupported Media Type\" };\n  public static RangeNotSatisfiable = { statusCode: 416, message: \"Range Not Satisfiable\" };\n  public static ExpectationFailed = { statusCode: 417, message: \"Expectation Failed\" };\n  public static ImATeapot = { statusCode: 418, message: \"I'm a teapot\" };\n  public static MisdirectedRequest = { statusCode: 421, message: \"Misdirected Request\" };\n  public static UnprocessableEntity = { statusCode: 422, message: \"Unprocessable Entity\" };\n  public static Locked = { statusCode: 423, message: \"Locked\" };\n  public static FailedDependency = { statusCode: 424, message: \"Failed Dependency\" };\n  public static TooEarly = { statusCode: 425, message: \"Too Early\" };\n  public static UpgradeRequired = { statusCode: 426, message: \"Upgrade Required\" };\n  public static PreconditionRequired = { statusCode: 428, message: \"Precondition Required\" };\n  public static TooManyRequests = { statusCode: 429, message: \"Too Many Requests\" };\n  public static RequestHeaderFieldsTooLarge = { statusCode: 431, message: \"Request Header Fields Too Large\" };\n  public static UnavailableForLegalReasons = { statusCode: 451, message: \"Unavailable For Legal Reasons\" };\n\n  public static InternalServerError = { statusCode: 500, message: \"Internal Server Error\" };\n  public static NotImplemented = { statusCode: 501, message: \"Not Implemented\" };\n  public static BadGateway = { statusCode: 502, message: \"Bad Gateway\" };\n  public static ServiceUnavailable = { statusCode: 503, message: \"Service Unavailable\" };\n  public static GatewayTimeout = { statusCode: 504, message: \"Gateway Timeout\" };\n  public static HTTPVersionNotSupported = { statusCode: 505, message: \"HTTP Version Not Supported\" };\n  public static VariantAlsoNegotiates = { statusCode: 506, message: \"Variant Also Negotiates\" };\n  public static InsufficientStorage = { statusCode: 507, message: \"Insufficient Storage\" };\n  public static LoopDetected = { statusCode: 508, message: \"Loop Detected\" };\n  public static NotExtended = { statusCode: 510, message: \"Not Extended\" };\n  public static NetworkAuthenticationRequired = { statusCode: 511, message: \"Network Authentication Required\" };\n\n\n  constructor(...args: StatusErrorConstructorParameters);\n  constructor(\n    status: number | Status,\n    message?: string,\n  ) {\n    if (typeof status === \"object\") {\n      message ??= status.message;\n      status = status.statusCode;\n    }\n    super(message);\n    this.statusCode = status;\n    if (!message) {\n      throw new StackAssertionError(\"StatusError always requires a message unless a Status object is passed\", { cause: this });\n    }\n  }\n\n  public static isStatusError(error: unknown): error is StatusError {\n    // like instanceof, but also works for errors thrown in other realms or by different versions of the same package\n    return typeof error === \"object\" && error !== null && \"__stackStatusErrorBrand\" in error && error.__stackStatusErrorBrand === \"stack-status-error-brand-sentinel\";\n  }\n\n  public isClientError() {\n    return this.statusCode >= 400 && this.statusCode < 500;\n  }\n\n  public isServerError() {\n    return !this.isClientError();\n  }\n\n  public getStatusCode(): number {\n    return this.statusCode;\n  }\n\n  public getBody(): Uint8Array {\n    return new TextEncoder().encode(this.message);\n  }\n\n  public getHeaders(): Record<string, string[]> {\n    return {\n      \"Content-Type\": [\"text/plain; charset=utf-8\"],\n    };\n  }\n\n  public toDescriptiveJson(): Json {\n    return {\n      status_code: this.getStatusCode(),\n      message: this.message,\n      headers: this.getHeaders(),\n    };\n  }\n\n  /**\n   * @deprecated this is not a good way to make status errors human-readable, use toDescriptiveJson instead\n   */\n  public toHttpJson(): Json {\n    return {\n      status_code: this.statusCode,\n      body: this.message,\n      headers: this.getHeaders(),\n    };\n  }\n}\nStatusError.prototype.name = \"StatusError\";\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAE1B,SAAS,YAAY;AACrB,SAAS,cAAc;AAMhB,SAAS,YAAY,MAAoB;AAC9C,MAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,UAAM,IAAI,oBAAoB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EAChD,WAAW,KAAK,CAAC,aAAa,OAAO;AACnC,UAAM,KAAK,CAAC;AAAA,EACd,OAAO;AAEL,UAAM,IAAI,YAAY,GAAG,IAAI;AAAA,EAC/B;AACF;AAEA,SAAS,yBAAyB,OAAuB;AAEvD,QAAM,eAAe,IAAI,MAAM,EAAE,OAAO,WAAW,SAAS;AAC5D,SAAO,MAAM,MAAM,IAAI,EAAE,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,IAAI;AAChE;AAwBO,SAAS,kBAAkB,UAAiB,QAAuB;AAExE,QAAM,qBAAqB,MAAM,OAAO,SAAS,IAAI;AAMrD,QAAM,YAAY,yBAAyB,IAAI,MAAM,EAAE,SAAS,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;AAGjF,aAAW,SAAS,QAAQ;AAC1B,UAAM,WAAW,yBAAyB,MAAM,SAAS,EAAE;AAC3D,UAAM,UAAU,qBAAqB,KAAK,QAAQ,YAAY,OAAO;AAAA,EACvE;AACF;AAGO,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAC7C,YAAY,SAAiC,WAAgD;AAC3F,UAAM,aAAa;AAAA;AAAA;AACnB,UAAM,GAAG,OAAO,GAAG,QAAQ,SAAS,UAAU,IAAI,KAAK,UAAU,IAAI,KAAK,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAF1D;AAI3C,WAAO,eAAe,MAAM,0BAA0B;AAAA,MACpD,MAAM;AACJ,eAAO,CAAC,KAAK,SAAS;AAAA,MACxB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,oBAAoB,UAAU,OAAO;AAG9B,SAAS,kBAAkB,OAAwB;AACxD,MAAI,EAAE,iBAAiB,OAAQ,QAAO,GAAG,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC;AACtE,SAAO,OAAO,OAAO,EAAE,UAAU,EAAE,CAAC;AACtC;AAGA,IAAM,aAAa,oBAAI,IAAyE;AACzF,SAAS,kBAAkB,MAAwD;AACxF,MAAI,WAAW,IAAI,IAAI,GAAG;AACxB;AAAA,EACF;AACA,aAAW,IAAI,IAAI;AACrB;AACA,kBAAkB,CAAC,UAAU,UAAU,cAAc;AACnD,UAAQ;AAAA,IACN,6BAA6B,QAAQ;AAAA;AAAA;AAAA,IAGrC,kBAAkB,KAAK;AAAA,IACvB,GAAG;AAAA,IACH;AAAA,EACF;AACF,CAAC;AACD,kBAAkB,CAAC,UAAU,UAAU,cAAc;AACnD,YAAU,sBAAsB,UAAU,uBAAuB,CAAC;AAClE,YAAU,oBAAoB,KAAK,EAAE,UAAU,OAAO,UAAU,CAAC;AACnE,CAAC;AAEM,SAAS,aAAa,UAAkB,OAAsB;AACnE,aAAW,QAAQ,YAAY;AAC7B;AAAA,MACE;AAAA,MACA;AAAA,MACA,GAAG,UAAU,OAAO,UAAU,YAAY,OAAO,UAAU,eAAe,4BAA4B,SAAS,MAAM,QAAQ,MAAM,sBAAsB,IAAK,MAAM,yBAAmC,CAAC;AAAA,IAC1M;AAAA,EACF;AACF;AAkBO,IAAM,cAAN,cAA0B,MAAM;AAAA,EAiDrC,YACE,QACA,SACA;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,kBAAY,OAAO;AACnB,eAAS,OAAO;AAAA,IAClB;AACA,UAAM,OAAO;AAxDf,SAAiB,0BAA0B;AAC3C,SAAO,OAAO;AAwDZ,SAAK,aAAa;AAClB,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,oBAAoB,0EAA0E,EAAE,OAAO,KAAK,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EAEA,OAAc,cAAc,OAAsC;AAEhE,WAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,6BAA6B,SAAS,MAAM,4BAA4B;AAAA,EAChI;AAAA,EAEO,gBAAgB;AACrB,WAAO,KAAK,cAAc,OAAO,KAAK,aAAa;AAAA,EACrD;AAAA,EAEO,gBAAgB;AACrB,WAAO,CAAC,KAAK,cAAc;AAAA,EAC7B;AAAA,EAEO,gBAAwB;AAC7B,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,UAAsB;AAC3B,WAAO,IAAI,YAAY,EAAE,OAAO,KAAK,OAAO;AAAA,EAC9C;AAAA,EAEO,aAAuC;AAC5C,WAAO;AAAA,MACL,gBAAgB,CAAC,2BAA2B;AAAA,IAC9C;AAAA,EACF;AAAA,EAEO,oBAA0B;AAC/B,WAAO;AAAA,MACL,aAAa,KAAK,cAAc;AAAA,MAChC,SAAS,KAAK;AAAA,MACd,SAAS,KAAK,WAAW;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKO,aAAmB;AACxB,WAAO;AAAA,MACL,aAAa,KAAK;AAAA,MAClB,MAAM,KAAK;AAAA,MACX,SAAS,KAAK,WAAW;AAAA,IAC3B;AAAA,EACF;AACF;AA7Ga,YAKG,aAAa,EAAE,YAAY,KAAK,SAAS,cAAc;AAL1D,YAMG,eAAe,EAAE,YAAY,KAAK,SAAS,eAAe;AAN7D,YAOG,kBAAkB,EAAE,YAAY,KAAK,SAAS,mBAAmB;AAPpE,YAQG,YAAY,EAAE,YAAY,KAAK,SAAS,YAAY;AARvD,YASG,WAAW,EAAE,YAAY,KAAK,SAAS,YAAY;AATtD,YAUG,mBAAmB,EAAE,YAAY,KAAK,SAAS,qBAAqB;AAVvE,YAWG,gBAAgB,EAAE,YAAY,KAAK,SAAS,iBAAiB;AAXhE,YAYG,8BAA8B,EAAE,YAAY,KAAK,SAAS,gCAAgC;AAZ7F,YAaG,iBAAiB,EAAE,YAAY,KAAK,SAAS,kBAAkB;AAblE,YAcG,WAAW,EAAE,YAAY,KAAK,SAAS,WAAW;AAdrD,YAeG,OAAO,EAAE,YAAY,KAAK,SAAS,OAAO;AAf7C,YAgBG,iBAAiB,EAAE,YAAY,KAAK,SAAS,kBAAkB;AAhBlE,YAiBG,qBAAqB,EAAE,YAAY,KAAK,SAAS,sBAAsB;AAjB1E,YAkBG,kBAAkB,EAAE,YAAY,KAAK,SAAS,oBAAoB;AAlBrE,YAmBG,aAAa,EAAE,YAAY,KAAK,SAAS,eAAe;AAnB3D,YAoBG,uBAAuB,EAAE,YAAY,KAAK,SAAS,yBAAyB;AApB/E,YAqBG,sBAAsB,EAAE,YAAY,KAAK,SAAS,wBAAwB;AArB7E,YAsBG,oBAAoB,EAAE,YAAY,KAAK,SAAS,qBAAqB;AAtBxE,YAuBG,YAAY,EAAE,YAAY,KAAK,SAAS,eAAe;AAvB1D,YAwBG,qBAAqB,EAAE,YAAY,KAAK,SAAS,sBAAsB;AAxB1E,YAyBG,sBAAsB,EAAE,YAAY,KAAK,SAAS,uBAAuB;AAzB5E,YA0BG,SAAS,EAAE,YAAY,KAAK,SAAS,SAAS;AA1BjD,YA2BG,mBAAmB,EAAE,YAAY,KAAK,SAAS,oBAAoB;AA3BtE,YA4BG,WAAW,EAAE,YAAY,KAAK,SAAS,YAAY;AA5BtD,YA6BG,kBAAkB,EAAE,YAAY,KAAK,SAAS,mBAAmB;AA7BpE,YA8BG,uBAAuB,EAAE,YAAY,KAAK,SAAS,wBAAwB;AA9B9E,YA+BG,kBAAkB,EAAE,YAAY,KAAK,SAAS,oBAAoB;AA/BrE,YAgCG,8BAA8B,EAAE,YAAY,KAAK,SAAS,kCAAkC;AAhC/F,YAiCG,6BAA6B,EAAE,YAAY,KAAK,SAAS,gCAAgC;AAjC5F,YAmCG,sBAAsB,EAAE,YAAY,KAAK,SAAS,wBAAwB;AAnC7E,YAoCG,iBAAiB,EAAE,YAAY,KAAK,SAAS,kBAAkB;AApClE,YAqCG,aAAa,EAAE,YAAY,KAAK,SAAS,cAAc;AArC1D,YAsCG,qBAAqB,EAAE,YAAY,KAAK,SAAS,sBAAsB;AAtC1E,YAuCG,iBAAiB,EAAE,YAAY,KAAK,SAAS,kBAAkB;AAvClE,YAwCG,0BAA0B,EAAE,YAAY,KAAK,SAAS,6BAA6B;AAxCtF,YAyCG,wBAAwB,EAAE,YAAY,KAAK,SAAS,0BAA0B;AAzCjF,YA0CG,sBAAsB,EAAE,YAAY,KAAK,SAAS,uBAAuB;AA1C5E,YA2CG,eAAe,EAAE,YAAY,KAAK,SAAS,gBAAgB;AA3C9D,YA4CG,cAAc,EAAE,YAAY,KAAK,SAAS,eAAe;AA5C5D,YA6CG,gCAAgC,EAAE,YAAY,KAAK,SAAS,kCAAkC;AAiE9G,YAAY,UAAU,OAAO;", "names": []}