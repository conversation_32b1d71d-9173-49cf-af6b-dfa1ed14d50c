import { jsx as _jsx } from "react/jsx-runtime";
import { ReloadIcon } from "@radix-ui/react-icons";
import { forwardRefIfNeeded } from "@stackframe/stack-shared/dist/utils/react";
import { cn } from "../../lib/utils";
export const Spinner = forwardRefIfNeeded(({ size = 15, ...props }, ref) => {
    return (_jsx("span", { ref: ref, ...props, className: cn("stack-scope", props.className), children: _jsx(ReloadIcon, { className: "animate-spin", width: size, height: size }) }));
});
Spinner.displayName = "Spinner";
