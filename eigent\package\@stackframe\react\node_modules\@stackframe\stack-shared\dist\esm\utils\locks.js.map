{"version": 3, "sources": ["../../../src/utils/locks.tsx"], "sourcesContent": ["import { Semaphore } from 'async-mutex';\n\ntype LockCallback<T> = () => Promise<T>;\n\nexport class ReadWriteLock {\n  private semaphore: Semaphore;\n  private readers: number;\n  private readersMutex: Semaphore;\n\n  constructor() {\n    this.semaphore = new Semaphore(1); // Semaphore with 1 permit\n    this.readers = 0; // Track the number of readers\n    this.readersMutex = new Semaphore(1); // Protect access to `readers` count\n  }\n\n  async withReadLock<T>(callback: LockCallback<T>): Promise<T> {\n    await this._acquireReadLock();\n    try {\n      return await callback();\n    } finally {\n      await this._releaseReadLock();\n    }\n  }\n\n  async withWriteLock<T>(callback: LockCallback<T>): Promise<T> {\n    await this._acquireWriteLock();\n    try {\n      return await callback();\n    } finally {\n      await this._releaseWriteLock();\n    }\n  }\n\n  private async _acquireReadLock(): Promise<void> {\n    // Increment the readers count\n    await this.readersMutex.acquire();\n    try {\n      this.readers += 1;\n      // If this is the first reader, block writers\n      if (this.readers === 1) {\n        await this.semaphore.acquire();\n      }\n    } finally {\n      this.readersMutex.release();\n    }\n  }\n\n  private async _releaseReadLock(): Promise<void> {\n    // Decrement the readers count\n    await this.readersMutex.acquire();\n    try {\n      this.readers -= 1;\n      // If this was the last reader, release the writer block\n      if (this.readers === 0) {\n        this.semaphore.release();\n      }\n    } finally {\n      this.readersMutex.release();\n    }\n  }\n\n  private async _acquireWriteLock(): Promise<void> {\n    // Writers acquire the main semaphore exclusively\n    await this.semaphore.acquire();\n  }\n\n  private async _releaseWriteLock(): Promise<void> {\n    // Writers release the main semaphore\n    this.semaphore.release();\n  }\n}\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAInB,IAAM,gBAAN,MAAoB;AAAA,EAKzB,cAAc;AACZ,SAAK,YAAY,IAAI,UAAU,CAAC;AAChC,SAAK,UAAU;AACf,SAAK,eAAe,IAAI,UAAU,CAAC;AAAA,EACrC;AAAA,EAEA,MAAM,aAAgB,UAAuC;AAC3D,UAAM,KAAK,iBAAiB;AAC5B,QAAI;AACF,aAAO,MAAM,SAAS;AAAA,IACxB,UAAE;AACA,YAAM,KAAK,iBAAiB;AAAA,IAC9B;AAAA,EACF;AAAA,EAEA,MAAM,cAAiB,UAAuC;AAC5D,UAAM,KAAK,kBAAkB;AAC7B,QAAI;AACF,aAAO,MAAM,SAAS;AAAA,IACxB,UAAE;AACA,YAAM,KAAK,kBAAkB;AAAA,IAC/B;AAAA,EACF;AAAA,EAEA,MAAc,mBAAkC;AAE9C,UAAM,KAAK,aAAa,QAAQ;AAChC,QAAI;AACF,WAAK,WAAW;AAEhB,UAAI,KAAK,YAAY,GAAG;AACtB,cAAM,KAAK,UAAU,QAAQ;AAAA,MAC/B;AAAA,IACF,UAAE;AACA,WAAK,aAAa,QAAQ;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAc,mBAAkC;AAE9C,UAAM,KAAK,aAAa,QAAQ;AAChC,QAAI;AACF,WAAK,WAAW;AAEhB,UAAI,KAAK,YAAY,GAAG;AACtB,aAAK,UAAU,QAAQ;AAAA,MACzB;AAAA,IACF,UAAE;AACA,WAAK,aAAa,QAAQ;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAc,oBAAmC;AAE/C,UAAM,KAAK,UAAU,QAAQ;AAAA,EAC/B;AAAA,EAEA,MAAc,oBAAmC;AAE/C,SAAK,UAAU,QAAQ;AAAA,EACzB;AACF;", "names": []}