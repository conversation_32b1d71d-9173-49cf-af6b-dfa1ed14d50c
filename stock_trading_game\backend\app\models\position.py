"""
🐾 持仓模型
Position Model

定义用户股票持仓表结构和相关业务逻辑
"""

from datetime import datetime
from sqlalchemy import Column, Integer, Float, DateTime, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Position(Base):
    """持仓模型"""
    
    __tablename__ = "positions"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="持仓ID")
    
    # 关联字段
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, comment="股票ID")
    
    # 持仓信息
    quantity = Column(Integer, default=0, comment="持仓数量")
    available_quantity = Column(Integer, default=0, comment="可用数量")
    frozen_quantity = Column(Integer, default=0, comment="冻结数量")
    
    # 成本信息
    avg_cost = Column(Float, default=0.0, comment="平均成本价")
    total_cost = Column(Float, default=0.0, comment="总成本")
    
    # 盈亏信息
    unrealized_profit = Column(Float, default=0.0, comment="未实现盈亏")
    unrealized_profit_rate = Column(Float, default=0.0, comment="未实现收益率")
    realized_profit = Column(Float, default=0.0, comment="已实现盈亏")
    
    # 统计信息
    total_buy_quantity = Column(Integer, default=0, comment="累计买入数量")
    total_sell_quantity = Column(Integer, default=0, comment="累计卖出数量")
    total_buy_amount = Column(Float, default=0.0, comment="累计买入金额")
    total_sell_amount = Column(Float, default=0.0, comment="累计卖出金额")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    first_buy_at = Column(DateTime(timezone=True), nullable=True, comment="首次买入时间")
    last_trade_at = Column(DateTime(timezone=True), nullable=True, comment="最后交易时间")
    
    # 关系定义
    user = relationship("User", back_populates="positions")
    stock = relationship("Stock", back_populates="positions")
    
    # 索引和约束
    __table_args__ = (
        UniqueConstraint('user_id', 'stock_id', name='uq_user_stock_position'),
        Index('idx_position_user_id', 'user_id'),
        Index('idx_position_stock_id', 'stock_id'),
        Index('idx_position_quantity', 'quantity'),
        Index('idx_position_updated_at', 'updated_at'),
    )
    
    def __repr__(self):
        return f"<Position(id={self.id}, user_id={self.user_id}, stock_id={self.stock_id}, quantity={self.quantity})>"
    
    @property
    def current_value(self) -> float:
        """当前市值"""
        if hasattr(self, 'stock') and self.stock:
            return self.quantity * self.stock.current_price
        return 0.0
    
    @property
    def profit_loss(self) -> float:
        """盈亏金额"""
        return self.current_value - self.total_cost
    
    @property
    def profit_loss_rate(self) -> float:
        """盈亏率"""
        if self.total_cost == 0:
            return 0.0
        return (self.profit_loss / self.total_cost) * 100
    
    @property
    def is_profitable(self) -> bool:
        """是否盈利"""
        return self.profit_loss > 0
    
    @property
    def weight_in_portfolio(self) -> float:
        """在投资组合中的权重"""
        if hasattr(self, 'user') and self.user and self.user.total_assets > 0:
            return (self.current_value / self.user.total_assets) * 100
        return 0.0
    
    def buy(self, quantity: int, price: float, fee: float = 0.0) -> None:
        """买入操作"""
        if quantity <= 0:
            return
        
        # 计算新的平均成本
        new_cost = quantity * price + fee
        total_quantity = self.quantity + quantity
        
        if total_quantity > 0:
            self.avg_cost = (self.total_cost + new_cost) / total_quantity
        
        # 更新持仓信息
        self.quantity = total_quantity
        self.available_quantity += quantity
        self.total_cost += new_cost
        
        # 更新统计信息
        self.total_buy_quantity += quantity
        self.total_buy_amount += quantity * price
        
        # 更新时间
        if self.first_buy_at is None:
            self.first_buy_at = datetime.utcnow()
        self.last_trade_at = datetime.utcnow()
    
    def sell(self, quantity: int, price: float, fee: float = 0.0) -> bool:
        """卖出操作"""
        if quantity <= 0 or quantity > self.available_quantity:
            return False
        
        # 计算已实现盈亏
        sell_cost = quantity * self.avg_cost
        sell_amount = quantity * price - fee
        realized_profit = sell_amount - sell_cost
        
        # 更新持仓信息
        self.quantity -= quantity
        self.available_quantity -= quantity
        self.total_cost -= sell_cost
        self.realized_profit += realized_profit
        
        # 更新统计信息
        self.total_sell_quantity += quantity
        self.total_sell_amount += quantity * price
        
        # 更新时间
        self.last_trade_at = datetime.utcnow()
        
        return True
    
    def freeze(self, quantity: int) -> bool:
        """冻结股票"""
        if quantity <= 0 or quantity > self.available_quantity:
            return False
        
        self.available_quantity -= quantity
        self.frozen_quantity += quantity
        return True
    
    def unfreeze(self, quantity: int) -> None:
        """解冻股票"""
        if quantity <= 0:
            return
        
        actual_quantity = min(quantity, self.frozen_quantity)
        self.frozen_quantity -= actual_quantity
        self.available_quantity += actual_quantity
    
    def update_market_value(self, current_price: float) -> None:
        """更新市值和盈亏"""
        if self.quantity > 0:
            current_value = self.quantity * current_price
            self.unrealized_profit = current_value - self.total_cost
            
            if self.total_cost > 0:
                self.unrealized_profit_rate = (self.unrealized_profit / self.total_cost) * 100
            else:
                self.unrealized_profit_rate = 0.0
    
    def clear_position(self) -> None:
        """清空持仓"""
        self.quantity = 0
        self.available_quantity = 0
        self.frozen_quantity = 0
        self.total_cost = 0.0
        self.avg_cost = 0.0
        self.unrealized_profit = 0.0
        self.unrealized_profit_rate = 0.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "stock_id": self.stock_id,
            "quantity": self.quantity,
            "available_quantity": self.available_quantity,
            "frozen_quantity": self.frozen_quantity,
            "avg_cost": self.avg_cost,
            "total_cost": self.total_cost,
            "current_value": self.current_value,
            "unrealized_profit": self.unrealized_profit,
            "unrealized_profit_rate": self.unrealized_profit_rate,
            "realized_profit": self.realized_profit,
            "profit_loss": self.profit_loss,
            "profit_loss_rate": self.profit_loss_rate,
            "is_profitable": self.is_profitable,
            "weight_in_portfolio": self.weight_in_portfolio,
            "total_buy_quantity": self.total_buy_quantity,
            "total_sell_quantity": self.total_sell_quantity,
            "total_buy_amount": self.total_buy_amount,
            "total_sell_amount": self.total_sell_amount,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "first_buy_at": self.first_buy_at.isoformat() if self.first_buy_at else None,
            "last_trade_at": self.last_trade_at.isoformat() if self.last_trade_at else None,
        }
