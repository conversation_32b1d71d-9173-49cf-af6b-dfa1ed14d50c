"""
🐾 交易记录模型
Transaction Model

定义交易成交记录表结构和相关业务逻辑
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Index, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class TransactionType(str, Enum):
    """交易类型"""
    BUY = "buy"          # 买入
    SELL = "sell"        # 卖出


class Transaction(Base):
    """交易记录模型"""
    
    __tablename__ = "transactions"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="交易ID")
    transaction_no = Column(String(32), unique=True, index=True, nullable=False, comment="交易编号")
    
    # 关联字段
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, comment="股票ID")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, comment="订单ID")
    
    # 交易信息
    transaction_type = Column(SQLEnum(TransactionType), nullable=False, comment="交易类型")
    price = Column(Float, nullable=False, comment="成交价格")
    quantity = Column(Integer, nullable=False, comment="成交数量")
    amount = Column(Float, nullable=False, comment="成交金额")
    fee = Column(Float, default=0.0, comment="手续费")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="成交时间")
    trade_date = Column(String(10), nullable=False, comment="交易日期 YYYY-MM-DD")
    
    # 市场信息
    market_price = Column(Float, nullable=True, comment="成交时市价")
    volume_before = Column(Integer, default=0, comment="成交前总成交量")
    volume_after = Column(Integer, default=0, comment="成交后总成交量")
    
    # 其他信息
    notes = Column(String(500), nullable=True, comment="备注")
    source = Column(String(50), default="web", comment="交易来源")
    
    # 关系定义
    user = relationship("User", back_populates="transactions")
    stock = relationship("Stock", back_populates="transactions")
    order = relationship("Order", back_populates="transactions")
    
    # 索引
    __table_args__ = (
        Index('idx_transaction_user_id', 'user_id'),
        Index('idx_transaction_stock_id', 'stock_id'),
        Index('idx_transaction_order_id', 'order_id'),
        Index('idx_transaction_type', 'transaction_type'),
        Index('idx_transaction_created_at', 'created_at'),
        Index('idx_transaction_trade_date', 'trade_date'),
        Index('idx_transaction_user_date', 'user_id', 'trade_date'),
        Index('idx_transaction_stock_date', 'stock_id', 'trade_date'),
    )
    
    def __repr__(self):
        return f"<Transaction(id={self.id}, type={self.transaction_type}, price={self.price}, quantity={self.quantity})>"
    
    @property
    def total_cost(self) -> float:
        """总成本（包含手续费）"""
        return self.amount + self.fee
    
    @property
    def unit_cost(self) -> float:
        """单位成本"""
        if self.quantity == 0:
            return 0.0
        return self.total_cost / self.quantity
    
    def calculate_profit(self, current_price: float) -> float:
        """计算盈亏（相对于当前价格）"""
        if self.transaction_type == TransactionType.BUY:
            # 买入：当前价格 - 成交价格
            return (current_price - self.price) * self.quantity - self.fee
        else:
            # 卖出：成交价格 - 当前价格（假设重新买入）
            return (self.price - current_price) * self.quantity - self.fee
    
    def calculate_profit_rate(self, current_price: float) -> float:
        """计算收益率"""
        if self.amount == 0:
            return 0.0
        
        profit = self.calculate_profit(current_price)
        return (profit / self.amount) * 100
    
    @classmethod
    def generate_transaction_no(cls) -> str:
        """生成交易编号"""
        import uuid
        import time
        timestamp = str(int(time.time()))
        unique_id = str(uuid.uuid4()).replace('-', '')[:8]
        return f"T{timestamp}{unique_id}".upper()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "transaction_no": self.transaction_no,
            "user_id": self.user_id,
            "stock_id": self.stock_id,
            "order_id": self.order_id,
            "transaction_type": self.transaction_type.value,
            "price": self.price,
            "quantity": self.quantity,
            "amount": self.amount,
            "fee": self.fee,
            "total_cost": self.total_cost,
            "unit_cost": self.unit_cost,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "trade_date": self.trade_date,
            "market_price": self.market_price,
            "volume_before": self.volume_before,
            "volume_after": self.volume_after,
            "notes": self.notes,
            "source": self.source,
        }
