"""
🐾 用户相关API端点
User Related API Endpoints

提供用户注册、登录、资产查询等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.models.user import User

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_users(
    skip: int = 0,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户列表（管理员功能）
    """
    result = await db.execute(
        select(User).where(User.is_active == True).offset(skip).limit(limit)
    )
    users = result.scalars().all()
    
    return [
        {
            "id": user.id,
            "username": user.username,
            "nickname": user.nickname,
            "email": user.email,
            "balance": user.balance,
            "total_assets": user.total_assets,
            "level": user.level,
            "created_at": user.created_at.isoformat() if user.created_at else None
        }
        for user in users
    ]


@router.get("/{user_id}", response_model=dict)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户详情
    """
    result = await db.execute(
        select(User).where(User.id == user_id, User.is_active == True)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return user.to_dict()


@router.get("/{user_id}/portfolio", response_model=dict)
async def get_user_portfolio(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户投资组合
    """
    # 获取用户信息
    result = await db.execute(
        select(User).where(User.id == user_id, User.is_active == True)
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 获取持仓信息
    from app.models.position import Position
    from app.models.stock import Stock
    from sqlalchemy.orm import selectinload
    
    result = await db.execute(
        select(Position).options(
            selectinload(Position.stock)
        ).where(
            Position.user_id == user_id,
            Position.quantity > 0
        )
    )
    positions = result.scalars().all()
    
    # 计算投资组合数据
    portfolio_data = {
        "user_id": user_id,
        "total_assets": user.total_assets,
        "available_balance": user.balance,
        "frozen_balance": user.frozen_balance,
        "total_market_value": 0.0,
        "total_profit": user.total_profit,
        "positions": []
    }
    
    total_market_value = 0.0
    
    for position in positions:
        if position.stock:
            market_value = position.quantity * position.stock.current_price
            total_market_value += market_value
            
            position_data = {
                "stock_id": position.stock_id,
                "stock_code": position.stock.code,
                "stock_name": position.stock.name,
                "quantity": position.quantity,
                "available_quantity": position.available_quantity,
                "avg_cost": position.avg_cost,
                "current_price": position.stock.current_price,
                "market_value": market_value,
                "profit_loss": position.profit_loss,
                "profit_loss_rate": position.profit_loss_rate,
                "weight": (market_value / user.total_assets * 100) if user.total_assets > 0 else 0
            }
            portfolio_data["positions"].append(position_data)
    
    portfolio_data["total_market_value"] = total_market_value
    
    return portfolio_data


@router.post("/", response_model=dict)
async def create_user(
    username: str,
    password: str,
    email: str,
    nickname: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    创建新用户（注册）
    """
    # 检查用户名是否已存在
    result = await db.execute(
        select(User).where(User.username == username)
    )
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    # 检查邮箱是否已存在
    result = await db.execute(
        select(User).where(User.email == email)
    )
    existing_email = result.scalar_one_or_none()
    
    if existing_email:
        raise HTTPException(status_code=400, detail="邮箱已被使用")
    
    # 创建新用户
    new_user = User(
        username=username,
        email=email,
        nickname=nickname or username
    )
    new_user.set_password(password)
    
    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)
    
    return {
        "id": new_user.id,
        "username": new_user.username,
        "nickname": new_user.nickname,
        "email": new_user.email,
        "balance": new_user.balance,
        "message": "用户创建成功"
    }
