import React from "react";
export type InputProps = {
    prefixItem?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;
export declare const Input: React.FC<{
    prefixItem?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement> & {
    ref?: React.Ref<HTMLInputElement> | undefined;
}>;
export type DelayedInputProps = {
    delay?: number;
} & InputProps;
export declare const DelayedInput: React.FC<{
    delay?: number | undefined;
} & {
    prefixItem?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement> & {
    ref?: React.Ref<HTMLInputElement> | undefined;
}>;
