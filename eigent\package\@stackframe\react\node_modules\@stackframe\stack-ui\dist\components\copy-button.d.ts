import React from "react";
declare const CopyButton: React.FC<{
    onClick?: ((e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void | Promise<void>) | undefined;
    loading?: boolean | undefined;
} & {
    asChild?: boolean | undefined;
} & React.ButtonHTMLAttributes<HTMLButtonElement> & import("class-variance-authority").VariantProps<(props?: ({
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "plain" | null | undefined;
    size?: "default" | "plain" | "sm" | "lg" | "icon" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
} & {
    content: string;
}>;
export { CopyButton };
