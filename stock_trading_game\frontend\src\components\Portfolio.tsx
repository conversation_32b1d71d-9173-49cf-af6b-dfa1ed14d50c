import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Table, 
  Typography, 
  Progress,
  Space,
  Tag,
  Button,
  Divider
} from 'antd';
import { 
  WalletOutlined, 
  TrophyOutlined, 
  RiseOutlined, 
  FallOutlined,
  PieChartOutlined 
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getUserPortfolio } from '../services/api';

const { Title, Text } = Typography;

interface Position {
  stock_id: number;
  stock_code: string;
  stock_name: string;
  quantity: number;
  available_quantity: number;
  avg_cost: number;
  current_price: number;
  market_value: number;
  profit_loss: number;
  profit_loss_rate: number;
  weight: number;
}

interface Portfolio {
  user_id: number;
  total_assets: number;
  available_balance: number;
  frozen_balance: number;
  total_market_value: number;
  total_profit: number;
  positions: Position[];
}

const Portfolio: React.FC = () => {
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadPortfolio();
    
    // 每30秒刷新一次
    const interval = setInterval(loadPortfolio, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadPortfolio = async () => {
    try {
      setLoading(true);
      // 假设用户ID为1，实际应该从登录状态获取
      const data = await getUserPortfolio(1);
      setPortfolio(data);
    } catch (error) {
      console.error('加载投资组合失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return '#ff4d4f';
    if (change < 0) return '#52c41a';
    return '#8c8c8c';
  };

  const formatNumber = (num: number) => {
    if (num >= 100000000) {
      return `${(num / 100000000).toFixed(2)}亿`;
    } else if (num >= 10000) {
      return `${(num / 10000).toFixed(2)}万`;
    }
    return num.toLocaleString();
  };

  const positionColumns: ColumnsType<Position> = [
    {
      title: '股票',
      key: 'stock',
      width: 120,
      render: (_, record: Position) => (
        <div>
          <Text strong>{record.stock_code}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.stock_name}
          </Text>
        </div>
      ),
    },
    {
      title: '持仓数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      align: 'right',
      render: (quantity: number) => quantity.toLocaleString(),
    },
    {
      title: '可用数量',
      dataIndex: 'available_quantity',
      key: 'available_quantity',
      width: 100,
      align: 'right',
      render: (quantity: number) => quantity.toLocaleString(),
    },
    {
      title: '成本价',
      dataIndex: 'avg_cost',
      key: 'avg_cost',
      width: 80,
      align: 'right',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '现价',
      dataIndex: 'current_price',
      key: 'current_price',
      width: 80,
      align: 'right',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '市值',
      dataIndex: 'market_value',
      key: 'market_value',
      width: 100,
      align: 'right',
      render: (value: number) => `¥${formatNumber(value)}`,
    },
    {
      title: '盈亏',
      key: 'profit_loss',
      width: 120,
      align: 'right',
      render: (_, record: Position) => (
        <div>
          <div style={{ color: getChangeColor(record.profit_loss) }}>
            {record.profit_loss > 0 ? '+' : ''}¥{record.profit_loss.toFixed(2)}
          </div>
          <div style={{ 
            color: getChangeColor(record.profit_loss_rate),
            fontSize: 12
          }}>
            {record.profit_loss_rate > 0 ? '+' : ''}{record.profit_loss_rate.toFixed(2)}%
          </div>
        </div>
      ),
    },
    {
      title: '占比',
      dataIndex: 'weight',
      key: 'weight',
      width: 80,
      align: 'right',
      render: (weight: number) => (
        <div>
          <Progress 
            percent={weight} 
            size="small" 
            showInfo={false}
            strokeColor={weight > 20 ? '#ff4d4f' : '#1890ff'}
          />
          <Text style={{ fontSize: 12 }}>{weight.toFixed(1)}%</Text>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record: Position) => (
        <Space>
          <Button type="link" size="small" style={{ color: '#ff4d4f' }}>
            买入
          </Button>
          <Button type="link" size="small" style={{ color: '#52c41a' }}>
            卖出
          </Button>
        </Space>
      ),
    },
  ];

  if (!portfolio) {
    return (
      <div style={{ padding: 24 }}>
        <Title level={2}>💼 我的资产</Title>
        <Card loading={loading} />
      </div>
    );
  }

  const totalProfitRate = portfolio.total_assets > 0 
    ? (portfolio.total_profit / (portfolio.total_assets - portfolio.total_profit)) * 100 
    : 0;

  return (
    <div style={{ padding: 24 }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        💼 我的资产
      </Title>

      {/* 资产概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总资产"
              value={portfolio.total_assets}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="可用资金"
              value={portfolio.available_balance}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="持仓市值"
              value={portfolio.total_market_value}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总盈亏"
              value={portfolio.total_profit}
              precision={2}
              prefix={portfolio.total_profit >= 0 ? '+¥' : '-¥'}
              valueStyle={{ color: getChangeColor(portfolio.total_profit) }}
              suffix={
                <span style={{ fontSize: 14 }}>
                  ({totalProfitRate > 0 ? '+' : ''}{totalProfitRate.toFixed(2)}%)
                </span>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 资产分布 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} md={12}>
          <Card title="资产分布" extra={<PieChartOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>现金资产</Text>
                <Text strong>
                  ¥{formatNumber(portfolio.available_balance + portfolio.frozen_balance)}
                </Text>
              </div>
              <Progress 
                percent={(portfolio.available_balance + portfolio.frozen_balance) / portfolio.total_assets * 100}
                strokeColor="#52c41a"
                showInfo={false}
              />
              
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>股票资产</Text>
                <Text strong>¥{formatNumber(portfolio.total_market_value)}</Text>
              </div>
              <Progress 
                percent={portfolio.total_market_value / portfolio.total_assets * 100}
                strokeColor="#1890ff"
                showInfo={false}
              />
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card title="投资统计" extra={<TrophyOutlined />}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="持仓股票"
                  value={portfolio.positions.length}
                  suffix="只"
                  prefix={<WalletOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="盈利股票"
                  value={portfolio.positions.filter(p => p.profit_loss > 0).length}
                  suffix="只"
                  prefix={<RiseOutlined />}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="亏损股票"
                  value={portfolio.positions.filter(p => p.profit_loss < 0).length}
                  suffix="只"
                  prefix={<FallOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="胜率"
                  value={portfolio.positions.length > 0 
                    ? (portfolio.positions.filter(p => p.profit_loss > 0).length / portfolio.positions.length * 100)
                    : 0
                  }
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 持仓明细 */}
      <Card title="持仓明细" extra={
        <Button onClick={loadPortfolio} loading={loading}>
          刷新
        </Button>
      }>
        <Table
          columns={positionColumns}
          dataSource={portfolio.positions}
          rowKey="stock_id"
          loading={loading}
          pagination={false}
          scroll={{ x: 800 }}
          size="small"
          summary={(pageData) => {
            const totalMarketValue = pageData.reduce((sum, record) => sum + record.market_value, 0);
            const totalProfitLoss = pageData.reduce((sum, record) => sum + record.profit_loss, 0);
            
            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={5}>
                  <Text strong>合计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}>
                  <Text strong>¥{formatNumber(totalMarketValue)}</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}>
                  <Text strong style={{ color: getChangeColor(totalProfitLoss) }}>
                    {totalProfitLoss > 0 ? '+' : ''}¥{totalProfitLoss.toFixed(2)}
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7}>
                  <Text strong>100.0%</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={8} />
              </Table.Summary.Row>
            );
          }}
        />
      </Card>
    </div>
  );
};

export default Portfolio;
