"""
🐾 股票相关API端点
Stock Related API Endpoints

提供股票查询、价格获取等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_db
from app.models.stock import Stock
from app.services.market_simulator import market_simulator

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_stocks(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    industry: Optional[str] = Query(None, description="行业筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取股票列表
    
    - **skip**: 跳过的记录数（分页）
    - **limit**: 返回的记录数（最多100条）
    - **industry**: 按行业筛选
    - **search**: 搜索股票代码或名称
    """
    query = select(Stock).where(Stock.is_active == True)
    
    # 行业筛选
    if industry:
        query = query.where(Stock.industry == industry)
    
    # 搜索筛选
    if search:
        search_term = f"%{search}%"
        query = query.where(
            and_(
                Stock.code.ilike(search_term) | 
                Stock.name.ilike(search_term)
            )
        )
    
    # 分页
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    stocks = result.scalars().all()
    
    return [stock.to_dict() for stock in stocks]


@router.get("/{stock_id}", response_model=dict)
async def get_stock(
    stock_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取单只股票详情
    
    - **stock_id**: 股票ID
    """
    result = await db.execute(
        select(Stock).where(
            and_(Stock.id == stock_id, Stock.is_active == True)
        )
    )
    stock = result.scalar_one_or_none()
    
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    return stock.to_dict()


@router.get("/code/{stock_code}", response_model=dict)
async def get_stock_by_code(
    stock_code: str,
    db: AsyncSession = Depends(get_db)
):
    """
    根据股票代码获取股票信息
    
    - **stock_code**: 股票代码（如：000001）
    """
    result = await db.execute(
        select(Stock).where(
            and_(Stock.code == stock_code, Stock.is_active == True)
        )
    )
    stock = result.scalar_one_or_none()
    
    if not stock:
        raise HTTPException(status_code=404, detail=f"股票代码 {stock_code} 不存在")
    
    return stock.to_dict()


@router.get("/industries/", response_model=List[dict])
async def get_industries(db: AsyncSession = Depends(get_db)):
    """
    获取所有行业分类
    """
    from sqlalchemy import func, distinct
    
    result = await db.execute(
        select(
            Stock.industry,
            func.count(Stock.id).label('count')
        ).where(
            and_(Stock.is_active == True, Stock.industry.isnot(None))
        ).group_by(Stock.industry).order_by(Stock.industry)
    )
    
    industries = result.all()
    
    return [
        {
            "industry": industry.industry,
            "count": industry.count
        }
        for industry in industries
    ]


@router.get("/{stock_id}/realtime", response_model=dict)
async def get_stock_realtime(stock_id: int):
    """
    获取股票实时数据
    
    - **stock_id**: 股票ID
    """
    # 从市场模拟器缓存中获取实时数据
    if stock_id not in market_simulator.stocks_cache:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    stock = market_simulator.stocks_cache[stock_id]
    
    # 获取价格历史（最近100个点）
    price_history = market_simulator.price_history.get(stock_id, [])[-100:]
    
    return {
        **stock.to_dict(),
        "price_history": price_history,
        "market_sentiment": market_simulator.market_sentiment,
        "is_market_open": market_simulator._is_market_open()
    }


@router.get("/{stock_id}/kline", response_model=List[dict])
async def get_stock_kline(
    stock_id: int,
    timeframe: str = Query("1d", description="时间周期: 1m, 5m, 15m, 30m, 1h, 1d"),
    limit: int = Query(100, ge=1, le=1000, description="返回的K线数量"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取股票K线数据
    
    - **stock_id**: 股票ID
    - **timeframe**: 时间周期
    - **limit**: 返回的K线数量
    """
    from app.models.market_data import MarketData, TimeFrame
    
    # 验证时间周期
    try:
        tf = TimeFrame(timeframe)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的时间周期")
    
    # 查询K线数据
    result = await db.execute(
        select(MarketData).where(
            and_(
                MarketData.stock_id == stock_id,
                MarketData.timeframe == tf
            )
        ).order_by(MarketData.timestamp.desc()).limit(limit)
    )
    
    klines = result.scalars().all()
    
    if not klines:
        # 如果没有历史数据，生成一些模拟数据
        return await _generate_mock_kline_data(stock_id, timeframe, limit, db)
    
    # 按时间正序返回
    return [kline.to_dict() for kline in reversed(klines)]


async def _generate_mock_kline_data(
    stock_id: int, 
    timeframe: str, 
    limit: int, 
    db: AsyncSession
) -> List[dict]:
    """生成模拟K线数据"""
    import random
    from datetime import datetime, timedelta
    
    # 获取股票信息
    result = await db.execute(select(Stock).where(Stock.id == stock_id))
    stock = result.scalar_one_or_none()
    
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    # 生成模拟K线数据
    klines = []
    base_price = stock.current_price
    current_time = datetime.now()
    
    # 根据时间周期确定时间间隔
    time_delta_map = {
        "1m": timedelta(minutes=1),
        "5m": timedelta(minutes=5),
        "15m": timedelta(minutes=15),
        "30m": timedelta(minutes=30),
        "1h": timedelta(hours=1),
        "1d": timedelta(days=1)
    }
    
    time_delta = time_delta_map.get(timeframe, timedelta(days=1))
    
    for i in range(limit):
        # 生成OHLC数据
        open_price = base_price * random.uniform(0.95, 1.05)
        high_price = open_price * random.uniform(1.0, 1.08)
        low_price = open_price * random.uniform(0.92, 1.0)
        close_price = random.uniform(low_price, high_price)
        volume = random.randint(1000, 100000)
        
        kline_time = current_time - time_delta * (limit - i)
        
        kline_data = {
            "timestamp": kline_time.isoformat(),
            "timeframe": timeframe,
            "open_price": round(open_price, 2),
            "high_price": round(high_price, 2),
            "low_price": round(low_price, 2),
            "close_price": round(close_price, 2),
            "volume": volume,
            "turnover": round(close_price * volume, 2)
        }
        
        klines.append(kline_data)
        base_price = close_price  # 下一根K线的基准价格
    
    return klines


@router.post("/{stock_id}/trigger-news")
async def trigger_news_event(
    stock_id: int,
    impact: float = Query(..., ge=-0.1, le=0.1, description="新闻影响(-10%到10%)")
):
    """
    触发股票新闻事件（测试用）
    
    - **stock_id**: 股票ID
    - **impact**: 影响程度（-0.1到0.1，即-10%到10%）
    """
    if stock_id not in market_simulator.stocks_cache:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    await market_simulator.trigger_news_event(stock_id, impact)
    
    return {
        "message": f"已触发股票 {stock_id} 的新闻事件",
        "impact": f"{impact:.2%}"
    }
