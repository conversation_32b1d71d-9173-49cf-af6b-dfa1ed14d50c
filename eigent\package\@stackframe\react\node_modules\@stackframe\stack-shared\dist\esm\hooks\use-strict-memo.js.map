{"version": 3, "sources": ["../../../src/hooks/use-strict-memo.tsx"], "sourcesContent": ["import { useId, useInsertionEffect, useMemo } from \"react\";\nimport { Result } from \"../utils/results\";\n\ntype CacheInner = Map<unknown, CacheInner> | WeakMap<WeakKey, CacheInner> | { isNotNestedMap: true, value: any };\n\nconst cached = new Map<string, CacheInner>();\n\nfunction unwrapFromInner(dependencies: any[], inner: CacheInner): Result<any, void> {\n  if ((dependencies.length === 0) !== (\"isNotNestedMap\" in inner)) {\n    return Result.error(undefined);\n  }\n  if (\"isNotNestedMap\" in inner) {\n    if (dependencies.length === 0) {\n      return Result.ok(inner.value);\n    } else {\n      return Result.error(undefined);\n    }\n  } else {\n    if (dependencies.length === 0) {\n      return Result.error(undefined);\n    } else {\n      const [key, ...rest] = dependencies;\n      const newInner = inner.get(key);\n      if (!newInner) {\n        return Result.error(undefined);\n      }\n      return unwrapFromInner(rest, newInner);\n    }\n  }\n}\nundefined?.test(\"unwrapFromInner\", ({ expect }) => {\n  // Test with empty dependencies and non-nested map\n  const nonNestedMap = { isNotNestedMap: true, value: \"test\" } as CacheInner;\n  const result1 = unwrapFromInner([], nonNestedMap);\n  expect(result1.status).toBe(\"ok\");\n  if (result1.status === \"ok\") {\n    expect(result1.data).toBe(\"test\");\n  }\n\n  // Test with non-empty dependencies and non-nested map (should error)\n  expect(unwrapFromInner([\"key\"], nonNestedMap).status).toBe(\"error\");\n\n  // Test with empty dependencies and nested map (should error)\n  const nestedMap = new Map([[\"key\", { isNotNestedMap: true, value: \"test\" } as CacheInner]]) as CacheInner;\n  expect(unwrapFromInner([], nestedMap).status).toBe(\"error\");\n\n  // Test with matching dependencies and nested map\n  const result2 = unwrapFromInner([\"key\"], nestedMap);\n  expect(result2.status).toBe(\"ok\");\n  if (result2.status === \"ok\") {\n    expect(result2.data).toBe(\"test\");\n  }\n\n  // Test with non-matching dependencies and nested map\n  expect(unwrapFromInner([\"wrongKey\"], nestedMap).status).toBe(\"error\");\n\n  // Test with deeply nested map\n  const deeplyNestedMap = new Map([\n    [\"key1\", new Map([\n      [\"key2\", { isNotNestedMap: true, value: \"nested\" } as CacheInner]\n    ]) as CacheInner]\n  ]) as CacheInner;\n\n  const result3 = unwrapFromInner([\"key1\", \"key2\"], deeplyNestedMap);\n  expect(result3.status).toBe(\"ok\");\n  if (result3.status === \"ok\") {\n    expect(result3.data).toBe(\"nested\");\n  }\n\n  // Test with partial match in deeply nested map\n  expect(unwrapFromInner([\"key1\", \"wrongKey\"], deeplyNestedMap).status).toBe(\"error\");\n});\n\nfunction wrapToInner(dependencies: any[], value: any): CacheInner {\n  if (dependencies.length === 0) {\n    return { isNotNestedMap: true, value };\n  }\n  const [key, ...rest] = dependencies;\n  const inner = wrapToInner(rest, value);\n\n  const isObject = (typeof key === \"object\" && key !== null);\n  const isUnregisteredSymbol = (typeof key === \"symbol\" && Symbol.keyFor(key) === undefined);\n  const isWeak = isObject || isUnregisteredSymbol;\n  const mapType = isWeak ? WeakMap : Map;\n\n  return new mapType([[key, inner]]);\n}\nundefined?.test(\"wrapToInner\", ({ expect }) => {\n  // Test with empty dependencies\n  const emptyResult = wrapToInner([], \"test\");\n  expect(emptyResult).toEqual({ isNotNestedMap: true, value: \"test\" });\n\n  // Test with single string dependency\n  const singleResult = wrapToInner([\"key\"], \"test\");\n  expect(singleResult instanceof Map).toBe(true);\n  // Need to cast to access Map methods\n  const singleMap = singleResult as Map<unknown, CacheInner>;\n  expect(singleMap.get(\"key\")).toEqual({ isNotNestedMap: true, value: \"test\" });\n\n  // Test with multiple string dependencies\n  const multiResult = wrapToInner([\"key1\", \"key2\"], \"test\");\n  expect(multiResult instanceof Map).toBe(true);\n  // Need to cast to access Map methods\n  const multiMap = multiResult as Map<unknown, CacheInner>;\n  const innerMap = multiMap.get(\"key1\") as Map<unknown, CacheInner>;\n  expect(innerMap instanceof Map).toBe(true);\n  expect(innerMap.get(\"key2\")).toEqual({ isNotNestedMap: true, value: \"test\" });\n\n  // Test with object dependency (should use WeakMap)\n  const obj = { test: true };\n  const objResult = wrapToInner([obj], \"test\");\n  expect(objResult instanceof WeakMap).toBe(true);\n  // Need to cast to access WeakMap methods\n  const objMap = objResult as WeakMap<WeakKey, CacheInner>;\n  expect(objMap.get(obj)).toEqual({ isNotNestedMap: true, value: \"test\" });\n\n  // Test with unregistered symbol dependency (should use WeakMap)\n  const symbolObj = Symbol(\"test\");\n  const symbolResult = wrapToInner([symbolObj], \"test\");\n  expect(symbolResult instanceof WeakMap).toBe(true);\n  // Need to cast to access WeakMap methods\n  const symbolMap = symbolResult as WeakMap<WeakKey, CacheInner>;\n  expect(symbolMap.get(symbolObj as unknown as object)).toEqual({ isNotNestedMap: true, value: \"test\" });\n\n  // Test with registered symbol dependency (should use Map)\n  const registeredSymbol = Symbol.for(\"test\");\n  const registeredSymbolResult = wrapToInner([registeredSymbol], \"test\");\n  expect(registeredSymbolResult instanceof Map).toBe(true);\n  // Need to cast to access Map methods\n  const registeredSymbolMap = registeredSymbolResult as Map<unknown, CacheInner>;\n  expect(registeredSymbolMap.get(registeredSymbol)).toEqual({ isNotNestedMap: true, value: \"test\" });\n});\n\n/**\n * Like memo, but minimizes recomputation of the value at all costs (instead of useMemo which recomputes whenever the renderer feels like it).\n *\n * The most recent value will be kept from garbage collection until one of the dependencies becomes unreachable. This may be true even after the component no longer renders. Be wary of memory leaks.\n */\nexport function useStrictMemo<T>(callback: () => T, dependencies: any[]): T {\n  const id = useId();\n  useInsertionEffect(() => {\n    return () => {\n      cached.delete(id);\n    };\n  }, [id]);\n\n  const c = cached.get(id);\n  if (c) {\n    const unwrapped = unwrapFromInner(dependencies, c);\n    if (unwrapped.status === \"ok\") {\n      return unwrapped.data;\n    }\n  }\n  const value = callback();\n  cached.set(id, wrapToInner(dependencies, value));\n  return value;\n}\n"], "mappings": ";AAAA,SAAS,OAAO,0BAAmC;AACnD,SAAS,cAAc;AAIvB,IAAM,SAAS,oBAAI,IAAwB;AAE3C,SAAS,gBAAgB,cAAqB,OAAsC;AAClF,MAAK,aAAa,WAAW,MAAQ,oBAAoB,OAAQ;AAC/D,WAAO,OAAO,MAAM,MAAS;AAAA,EAC/B;AACA,MAAI,oBAAoB,OAAO;AAC7B,QAAI,aAAa,WAAW,GAAG;AAC7B,aAAO,OAAO,GAAG,MAAM,KAAK;AAAA,IAC9B,OAAO;AACL,aAAO,OAAO,MAAM,MAAS;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,QAAI,aAAa,WAAW,GAAG;AAC7B,aAAO,OAAO,MAAM,MAAS;AAAA,IAC/B,OAAO;AACL,YAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,YAAM,WAAW,MAAM,IAAI,GAAG;AAC9B,UAAI,CAAC,UAAU;AACb,eAAO,OAAO,MAAM,MAAS;AAAA,MAC/B;AACA,aAAO,gBAAgB,MAAM,QAAQ;AAAA,IACvC;AAAA,EACF;AACF;AA4CA,SAAS,YAAY,cAAqB,OAAwB;AAChE,MAAI,aAAa,WAAW,GAAG;AAC7B,WAAO,EAAE,gBAAgB,MAAM,MAAM;AAAA,EACvC;AACA,QAAM,CAAC,KAAK,GAAG,IAAI,IAAI;AACvB,QAAM,QAAQ,YAAY,MAAM,KAAK;AAErC,QAAM,WAAY,OAAO,QAAQ,YAAY,QAAQ;AACrD,QAAM,uBAAwB,OAAO,QAAQ,YAAY,OAAO,OAAO,GAAG,MAAM;AAChF,QAAM,SAAS,YAAY;AAC3B,QAAM,UAAU,SAAS,UAAU;AAEnC,SAAO,IAAI,QAAQ,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;AACnC;AAoDO,SAAS,cAAiB,UAAmB,cAAwB;AAC1E,QAAM,KAAK,MAAM;AACjB,qBAAmB,MAAM;AACvB,WAAO,MAAM;AACX,aAAO,OAAO,EAAE;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,EAAE,CAAC;AAEP,QAAM,IAAI,OAAO,IAAI,EAAE;AACvB,MAAI,GAAG;AACL,UAAM,YAAY,gBAAgB,cAAc,CAAC;AACjD,QAAI,UAAU,WAAW,MAAM;AAC7B,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACA,QAAM,QAAQ,SAAS;AACvB,SAAO,IAAI,IAAI,YAAY,cAAc,KAAK,CAAC;AAC/C,SAAO;AACT;", "names": []}