{"version": 3, "sources": ["../../../src/utils/env.tsx"], "sourcesContent": ["import { throwErr } from \"./errors\";\nimport { deindent } from \"./strings\";\n\nexport function isBrowserLike() {\n  return typeof window !== \"undefined\" && typeof document !== \"undefined\" && typeof document.createElement !== \"undefined\";\n}\n\n// newName: oldName\nconst ENV_VAR_RENAME: Record<string, string[]> = {\n  NEXT_PUBLIC_STACK_API_URL: ['STACK_BASE_URL', 'NEXT_PUBLIC_STACK_URL'],\n};\n\n/**\n * Returns the environment variable with the given name, returning the default (if given) or throwing an error (otherwise) if it's undefined or the empty string.\n */\nexport function getEnvVariable(name: string, defaultValue?: string | undefined): string {\n  if (isBrowserLike()) {\n    throw new Error(deindent`\n      Can't use getEnvVariable on the client because Next.js transpiles expressions of the kind process.env.XYZ at build-time on the client.\n    \n      Use process.env.XYZ directly instead.\n    `);\n  }\n  if (name === \"NEXT_RUNTIME\") {\n    throw new Error(deindent`\n      Can't use getEnvVariable to access the NEXT_RUNTIME environment variable because it's compiled into the client bundle.\n    \n      Use getNextRuntime() instead.\n    `);\n  }\n\n  // throw error if the old name is used as the retrieve key\n  for (const [newName, oldNames] of Object.entries(ENV_VAR_RENAME)) {\n    if (oldNames.includes(name)) {\n      throwErr(`Environment variable ${name} has been renamed to ${newName}. Please update your configuration to use the new name.`);\n    }\n  }\n\n  let value = process.env[name];\n\n  // check the key under the old name if the new name is not found\n  if (!value && ENV_VAR_RENAME[name] as any) {\n    for (const oldName of ENV_VAR_RENAME[name]) {\n      value = process.env[oldName];\n      if (value) break;\n    }\n  }\n\n  if (value === undefined) {\n    if (defaultValue !== undefined) {\n      value = defaultValue;\n    } else {\n      throwErr(`Missing environment variable: ${name}`);\n    }\n  }\n\n  return value;\n}\n\nexport function getNextRuntime() {\n  // This variable is compiled into the client bundle, so we can't use getEnvVariable here.\n  return process.env.NEXT_RUNTIME || throwErr(\"Missing environment variable: NEXT_RUNTIME\");\n}\n\nexport function getNodeEnvironment() {\n  return getEnvVariable(\"NODE_ENV\", \"\");\n}\n"], "mappings": ";AAAA,SAAS,gBAAgB;AACzB,SAAS,gBAAgB;AAElB,SAAS,gBAAgB;AAC9B,SAAO,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,OAAO,SAAS,kBAAkB;AAC/G;AAGA,IAAM,iBAA2C;AAAA,EAC/C,2BAA2B,CAAC,kBAAkB,uBAAuB;AACvE;AAKO,SAAS,eAAe,MAAc,cAA2C;AACtF,MAAI,cAAc,GAAG;AACnB,UAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA,KAIf;AAAA,EACH;AACA,MAAI,SAAS,gBAAgB;AAC3B,UAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA,KAIf;AAAA,EACH;AAGA,aAAW,CAAC,SAAS,QAAQ,KAAK,OAAO,QAAQ,cAAc,GAAG;AAChE,QAAI,SAAS,SAAS,IAAI,GAAG;AAC3B,eAAS,wBAAwB,IAAI,wBAAwB,OAAO,yDAAyD;AAAA,IAC/H;AAAA,EACF;AAEA,MAAI,QAAQ,QAAQ,IAAI,IAAI;AAG5B,MAAI,CAAC,SAAS,eAAe,IAAI,GAAU;AACzC,eAAW,WAAW,eAAe,IAAI,GAAG;AAC1C,cAAQ,QAAQ,IAAI,OAAO;AAC3B,UAAI,MAAO;AAAA,IACb;AAAA,EACF;AAEA,MAAI,UAAU,QAAW;AACvB,QAAI,iBAAiB,QAAW;AAC9B,cAAQ;AAAA,IACV,OAAO;AACL,eAAS,iCAAiC,IAAI,EAAE;AAAA,IAClD;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,iBAAiB;AAE/B,SAAO,QAAQ,IAAI,gBAAgB,SAAS,4CAA4C;AAC1F;AAEO,SAAS,qBAAqB;AACnC,SAAO,eAAe,YAAY,EAAE;AACtC;", "names": []}