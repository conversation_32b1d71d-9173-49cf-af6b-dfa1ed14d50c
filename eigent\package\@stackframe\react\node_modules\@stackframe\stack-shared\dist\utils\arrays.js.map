{"version": 3, "sources": ["../../src/utils/arrays.tsx"], "sourcesContent": ["import { remainder } from \"./math\";\n\nexport function typedIncludes<T extends readonly any[]>(arr: T, item: unknown): item is T[number] {\n  return arr.includes(item);\n}\nundefined?.test(\"typedIncludes\", ({ expect }) => {\n  const arr = [1, 2, 3] as const;\n  expect(typedIncludes(arr, 1)).toBe(true);\n  expect(typedIncludes(arr, 4)).toBe(false);\n  expect(typedIncludes(arr, \"1\")).toBe(false);\n\n  const strArr = [\"a\", \"b\", \"c\"] as const;\n  expect(typedIncludes(strArr, \"a\")).toBe(true);\n  expect(typedIncludes(strArr, \"d\")).toBe(false);\n});\n\nexport function enumerate<T extends readonly any[]>(arr: T): [number, T[number]][] {\n  return arr.map((item, index) => [index, item]);\n}\nundefined?.test(\"enumerate\", ({ expect }) => {\n  expect(enumerate([])).toEqual([]);\n  expect(enumerate([1, 2, 3])).toEqual([[0, 1], [1, 2], [2, 3]]);\n  expect(enumerate([\"a\", \"b\", \"c\"])).toEqual([[0, \"a\"], [1, \"b\"], [2, \"c\"]]);\n});\n\nexport function isShallowEqual(a: readonly any[], b: readonly any[]): boolean {\n  if (a.length !== b.length) return false;\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) return false;\n  }\n  return true;\n}\nundefined?.test(\"isShallowEqual\", ({ expect }) => {\n  expect(isShallowEqual([], [])).toBe(true);\n  expect(isShallowEqual([1, 2, 3], [1, 2, 3])).toBe(true);\n  expect(isShallowEqual([1, 2, 3], [1, 2, 4])).toBe(false);\n  expect(isShallowEqual([1, 2, 3], [1, 2])).toBe(false);\n  expect(isShallowEqual([1, 2], [1, 2, 3])).toBe(false);\n  // Test with objects (reference equality)\n  const obj1 = { a: 1 };\n  const obj2 = { a: 1 };\n  expect(isShallowEqual([obj1], [obj1])).toBe(true);\n  expect(isShallowEqual([obj1], [obj2])).toBe(false);\n});\n\n/**\n * Ponyfill for ES2023's findLastIndex.\n */\nexport function findLastIndex<T>(arr: readonly T[], predicate: (item: T) => boolean): number {\n  for (let i = arr.length - 1; i >= 0; i--) {\n    if (predicate(arr[i])) return i;\n  }\n  return -1;\n}\nundefined?.test(\"findLastIndex\", ({ expect }) => {\n  expect(findLastIndex([], () => true)).toBe(-1);\n  expect(findLastIndex([1, 2, 3, 4, 5], x => x % 2 === 0)).toBe(3); // 4 is at index 3\n  expect(findLastIndex([1, 2, 3, 4, 5], x => x > 10)).toBe(-1);\n  expect(findLastIndex([1, 2, 3, 2, 1], x => x === 2)).toBe(3);\n  expect(findLastIndex([1, 2, 3], x => x === 1)).toBe(0);\n});\n\nexport function groupBy<T extends any, K>(\n  arr: Iterable<T>,\n  key: (item: T) => K,\n): Map<K, T[]> {\n  const result = new Map<K, T[]>;\n  for (const item of arr) {\n    const k = key(item);\n    if (result.get(k) === undefined) result.set(k, []);\n    result.get(k)!.push(item);\n  }\n  return result;\n}\nundefined?.test(\"groupBy\", ({ expect }) => {\n  expect(groupBy([], (x) => x)).toEqual(new Map());\n\n  const numbers = [1, 2, 3, 4, 5, 6];\n  const grouped = groupBy(numbers, (n) => n % 2 === 0 ? \"even\" : \"odd\");\n  expect(grouped.get(\"even\")).toEqual([2, 4, 6]);\n  expect(grouped.get(\"odd\")).toEqual([1, 3, 5]);\n\n  // Check the actual lengths of the words to ensure our test is correct\n  const words = [\"apple\", \"banana\", \"cherry\", \"date\", \"elderberry\"];\n\n  const byLength = groupBy(words, (w) => w.length);\n  // Adjust expectations based on actual word lengths\n  expect(byLength.get(5)).toEqual([\"apple\"]);\n  expect(byLength.get(6)).toEqual([\"banana\", \"cherry\"]);\n  expect(byLength.get(4)).toEqual([\"date\"]);\n  expect(byLength.get(10)).toEqual([\"elderberry\"]);\n});\n\nexport function range(endExclusive: number): number[];\nexport function range(startInclusive: number, endExclusive: number): number[];\nexport function range(startInclusive: number, endExclusive: number, step: number): number[];\nexport function range(startInclusive: number, endExclusive?: number, step?: number): number[] {\n  if (endExclusive === undefined) {\n    endExclusive = startInclusive;\n    startInclusive = 0;\n  }\n  if (step === undefined) step = 1;\n\n  const result = [];\n  for (let i = startInclusive; step > 0 ? (i < endExclusive) : (i > endExclusive); i += step) {\n    result.push(i);\n  }\n  return result;\n}\nundefined?.test(\"range\", ({ expect }) => {\n  expect(range(5)).toEqual([0, 1, 2, 3, 4]);\n  expect(range(2, 5)).toEqual([2, 3, 4]);\n  expect(range(1, 10, 2)).toEqual([1, 3, 5, 7, 9]);\n  expect(range(5, 0, -1)).toEqual([5, 4, 3, 2, 1]);\n  expect(range(0, 0)).toEqual([]);\n  expect(range(0, 10, 3)).toEqual([0, 3, 6, 9]);\n});\n\n\nexport function rotateLeft(arr: readonly any[], n: number): any[] {\n  if (arr.length === 0) return [];\n  const index = remainder(n, arr.length);\n  return [...arr.slice(index), ...arr.slice(0, index)];\n}\nundefined?.test(\"rotateLeft\", ({ expect }) => {\n  expect(rotateLeft([], 1)).toEqual([]);\n  expect(rotateLeft([1, 2, 3, 4, 5], 0)).toEqual([1, 2, 3, 4, 5]);\n  expect(rotateLeft([1, 2, 3, 4, 5], 1)).toEqual([2, 3, 4, 5, 1]);\n  expect(rotateLeft([1, 2, 3, 4, 5], 3)).toEqual([4, 5, 1, 2, 3]);\n  expect(rotateLeft([1, 2, 3, 4, 5], 5)).toEqual([1, 2, 3, 4, 5]);\n  expect(rotateLeft([1, 2, 3, 4, 5], 6)).toEqual([2, 3, 4, 5, 1]);\n});\n\nexport function rotateRight(arr: readonly any[], n: number): any[] {\n  return rotateLeft(arr, -n);\n}\nundefined?.test(\"rotateRight\", ({ expect }) => {\n  expect(rotateRight([], 1)).toEqual([]);\n  expect(rotateRight([1, 2, 3, 4, 5], 0)).toEqual([1, 2, 3, 4, 5]);\n  expect(rotateRight([1, 2, 3, 4, 5], 1)).toEqual([5, 1, 2, 3, 4]);\n  expect(rotateRight([1, 2, 3, 4, 5], 3)).toEqual([3, 4, 5, 1, 2]);\n  expect(rotateRight([1, 2, 3, 4, 5], 5)).toEqual([1, 2, 3, 4, 5]);\n  expect(rotateRight([1, 2, 3, 4, 5], 6)).toEqual([5, 1, 2, 3, 4]);\n});\n\n\nexport function shuffle<T>(arr: readonly T[]): T[] {\n  const result = [...arr];\n  for (let i = result.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [result[i], result[j]] = [result[j], result[i]];\n  }\n  return result;\n}\nundefined?.test(\"shuffle\", ({ expect }) => {\n  // Test empty array\n  expect(shuffle([])).toEqual([]);\n\n  // Test single element array\n  expect(shuffle([1])).toEqual([1]);\n\n  // Test that shuffle returns a new array\n  const original = [1, 2, 3, 4, 5];\n  const shuffled = shuffle(original);\n  expect(shuffled).not.toBe(original);\n\n  // Test that all elements are preserved\n  expect(shuffled.sort((a, b) => a - b)).toEqual(original);\n\n  // Test with a larger array to ensure randomness\n  // This is a probabilistic test, but it's very unlikely to fail\n  const large = Array.from({ length: 100 }, (_, i) => i);\n  const shuffledLarge = shuffle(large);\n  expect(shuffledLarge).not.toEqual(large);\n  expect(shuffledLarge.sort((a, b) => a - b)).toEqual(large);\n});\n\n\nexport function outerProduct<T, U>(arr1: readonly T[], arr2: readonly U[]): [T, U][] {\n  return arr1.flatMap((item1) => arr2.map((item2) => [item1, item2] as [T, U]));\n}\nundefined?.test(\"outerProduct\", ({ expect }) => {\n  expect(outerProduct([], [])).toEqual([]);\n  expect(outerProduct([1], [])).toEqual([]);\n  expect(outerProduct([], [1])).toEqual([]);\n  expect(outerProduct([1], [2])).toEqual([[1, 2]]);\n  expect(outerProduct([1, 2], [3, 4])).toEqual([[1, 3], [1, 4], [2, 3], [2, 4]]);\n  expect(outerProduct([\"a\", \"b\"], [1, 2])).toEqual([[\"a\", 1], [\"a\", 2], [\"b\", 1], [\"b\", 2]]);\n});\n\nexport function unique<T>(arr: readonly T[]): T[] {\n  return [...new Set(arr)];\n}\nundefined?.test(\"unique\", ({ expect }) => {\n  expect(unique([])).toEqual([]);\n  expect(unique([1, 2, 3])).toEqual([1, 2, 3]);\n  expect(unique([1, 2, 2, 3, 1, 3])).toEqual([1, 2, 3]);\n  // Test with objects (reference equality)\n  const obj = { a: 1 };\n  expect(unique([obj, obj])).toEqual([obj]);\n  // Test with different types\n  expect(unique([1, \"1\", true, 1, \"1\", true])).toEqual([1, \"1\", true]);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAA0B;AAEnB,SAAS,cAAwC,KAAQ,MAAkC;AAChG,SAAO,IAAI,SAAS,IAAI;AAC1B;AAYO,SAAS,UAAoC,KAA+B;AACjF,SAAO,IAAI,IAAI,CAAC,MAAM,UAAU,CAAC,OAAO,IAAI,CAAC;AAC/C;AAOO,SAAS,eAAe,GAAmB,GAA4B;AAC5E,MAAI,EAAE,WAAW,EAAE,OAAQ,QAAO;AAClC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAG,QAAO;AAAA,EAC5B;AACA,SAAO;AACT;AAiBO,SAAS,cAAiB,KAAmB,WAAyC;AAC3F,WAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,QAAI,UAAU,IAAI,CAAC,CAAC,EAAG,QAAO;AAAA,EAChC;AACA,SAAO;AACT;AASO,SAAS,QACd,KACA,KACa;AACb,QAAM,SAAS,oBAAI;AACnB,aAAW,QAAQ,KAAK;AACtB,UAAM,IAAI,IAAI,IAAI;AAClB,QAAI,OAAO,IAAI,CAAC,MAAM,OAAW,QAAO,IAAI,GAAG,CAAC,CAAC;AACjD,WAAO,IAAI,CAAC,EAAG,KAAK,IAAI;AAAA,EAC1B;AACA,SAAO;AACT;AAuBO,SAAS,MAAM,gBAAwB,cAAuB,MAAyB;AAC5F,MAAI,iBAAiB,QAAW;AAC9B,mBAAe;AACf,qBAAiB;AAAA,EACnB;AACA,MAAI,SAAS,OAAW,QAAO;AAE/B,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,gBAAgB,OAAO,IAAK,IAAI,eAAiB,IAAI,cAAe,KAAK,MAAM;AAC1F,WAAO,KAAK,CAAC;AAAA,EACf;AACA,SAAO;AACT;AAWO,SAAS,WAAW,KAAqB,GAAkB;AAChE,MAAI,IAAI,WAAW,EAAG,QAAO,CAAC;AAC9B,QAAM,YAAQ,uBAAU,GAAG,IAAI,MAAM;AACrC,SAAO,CAAC,GAAG,IAAI,MAAM,KAAK,GAAG,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC;AACrD;AAUO,SAAS,YAAY,KAAqB,GAAkB;AACjE,SAAO,WAAW,KAAK,CAAC,CAAC;AAC3B;AAWO,SAAS,QAAW,KAAwB;AACjD,QAAM,SAAS,CAAC,GAAG,GAAG;AACtB,WAAS,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,KAAK;AAC1C,UAAM,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI,EAAE;AAC5C,KAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,EAChD;AACA,SAAO;AACT;AAyBO,SAAS,aAAmB,MAAoB,MAA8B;AACnF,SAAO,KAAK,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,CAAW,CAAC;AAC9E;AAUO,SAAS,OAAU,KAAwB;AAChD,SAAO,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC;AACzB;", "names": []}