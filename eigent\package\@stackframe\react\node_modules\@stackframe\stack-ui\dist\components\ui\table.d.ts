import React from "react";
declare const Table: React.FC<React.HTMLAttributes<HTMLTableElement> & {
    ref?: React.Ref<HTMLTableElement> | undefined;
}>;
declare const TableHeader: React.FC<React.HTMLAttributes<HTMLTableSectionElement> & {
    ref?: React.Ref<HTMLTableSectionElement> | undefined;
}>;
declare const TableBody: React.FC<React.HTMLAttributes<HTMLTableSectionElement> & {
    ref?: React.Ref<HTMLTableSectionElement> | undefined;
}>;
declare const TableFooter: React.FC<React.HTMLAttributes<HTMLTableSectionElement> & {
    ref?: React.Ref<HTMLTableSectionElement> | undefined;
}>;
declare const TableRow: React.FC<React.HTMLAttributes<HTMLTableRowElement> & {
    ref?: React.Ref<HTMLTableRowElement> | undefined;
}>;
declare const TableHead: React.FC<React.ThHTMLAttributes<HTMLTableCellElement> & {
    ref?: React.Ref<HTMLTableCellElement> | undefined;
}>;
declare const TableCell: React.FC<React.TdHTMLAttributes<HTMLTableCellElement> & {
    ref?: React.Ref<HTMLTableCellElement> | undefined;
}>;
declare const TableCaption: React.FC<React.HTMLAttributes<HTMLTableCaptionElement> & {
    ref?: React.Ref<HTMLTableCaptionElement> | undefined;
}>;
export { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow };
