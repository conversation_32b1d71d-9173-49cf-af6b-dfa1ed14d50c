import * as LabelPrimitive from "@radix-ui/react-label";
import React from "react";
import { ControllerProps, FieldPath, FieldValues } from "react-hook-form";
declare const Form: <TFieldValues extends FieldValues, TContext = any, TTransformedValues extends FieldValues | undefined = undefined>(props: import("react-hook-form").FormProviderProps<TFieldValues, TContext, TTransformedValues>) => React.JSX.Element;
declare const FormField: <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({ ...props }: ControllerProps<TFieldValues, TName>) => import("react/jsx-runtime").JSX.Element;
declare const useFormField: () => {
    invalid: boolean;
    isDirty: boolean;
    isTouched: boolean;
    isValidating: boolean;
    error?: import("react-hook-form").FieldError | undefined;
    id: string;
    name: string;
    formItemId: string;
    formDescriptionId: string;
    formMessageId: string;
};
declare const FormItem: React.FC<React.HTMLAttributes<HTMLDivElement> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const FormLabel: React.FC<Omit<LabelPrimitive.LabelProps & React.RefAttributes<HTMLLabelElement>, "ref"> & {
    ref?: React.Ref<HTMLLabelElement> | undefined;
}>;
declare const FormControl: React.FC<Omit<import("@radix-ui/react-slot").SlotProps & React.RefAttributes<HTMLElement>, "ref"> & {
    ref?: React.Ref<HTMLElement> | undefined;
}>;
declare const FormDescription: React.FC<React.HTMLAttributes<HTMLParagraphElement> & {
    ref?: React.Ref<HTMLParagraphElement> | undefined;
}>;
declare const FormMessage: React.FC<React.HTMLAttributes<HTMLParagraphElement> & {
    ref?: React.Ref<HTMLParagraphElement> | undefined;
}>;
export { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage, useFormField };
