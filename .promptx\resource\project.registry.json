{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T09:30:00.963Z", "updatedAt": "2025-07-30T09:30:00.985Z", "resourceCount": 29}, "resources": [{"id": "workspace-structure", "source": "project", "protocol": "knowledge", "name": "Workspace Structure 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/knowledge/workspace-structure.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.967Z", "updatedAt": "2025-07-30T09:30:00.967Z", "scannedAt": "2025-07-30T09:30:00.967Z", "path": "knowledge/workspace-structure.knowledge.md"}}, {"id": "catgirl", "source": "project", "protocol": "role", "name": "Catgirl 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/catgirl/catgirl.role.md", "metadata": {"createdAt": "2025-07-30T09:30:00.968Z", "updatedAt": "2025-07-30T09:30:00.968Z", "scannedAt": "2025-07-30T09:30:00.968Z", "path": "role/catgirl/catgirl.role.md"}}, {"id": "catgirl-behavior", "source": "project", "protocol": "execution", "name": "Catgirl Behavior 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/catgirl/execution/catgirl-behavior.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.969Z", "updatedAt": "2025-07-30T09:30:00.969Z", "scannedAt": "2025-07-30T09:30:00.969Z", "path": "role/catgirl/execution/catgirl-behavior.execution.md"}}, {"id": "catgirl-thinking", "source": "project", "protocol": "thought", "name": "Catgirl Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/catgirl/thought/catgirl-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:30:00.970Z", "updatedAt": "2025-07-30T09:30:00.970Z", "scannedAt": "2025-07-30T09:30:00.970Z", "path": "role/catgirl/thought/catgirl-thinking.thought.md"}}, {"id": "da<PERSON><PERSON>", "source": "project", "protocol": "role", "name": "Daxiong 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/daxiong/daxiong.role.md", "metadata": {"createdAt": "2025-07-30T09:30:00.970Z", "updatedAt": "2025-07-30T09:30:00.970Z", "scannedAt": "2025-07-30T09:30:00.970Z", "path": "role/daxiong/daxiong.role.md"}}, {"id": "anti-ai-detection", "source": "project", "protocol": "execution", "name": "Anti Ai Detection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/anti-ai-detection.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.971Z", "updatedAt": "2025-07-30T09:30:00.971Z", "scannedAt": "2025-07-30T09:30:00.971Z", "path": "role/daxiong/execution/anti-ai-detection.execution.md"}}, {"id": "daxiong-workflow", "source": "project", "protocol": "execution", "name": "Daxiong Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/daxiong-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.972Z", "updatedAt": "2025-07-30T09:30:00.972Z", "scannedAt": "2025-07-30T09:30:00.972Z", "path": "role/daxiong/execution/daxiong-workflow.execution.md"}}, {"id": "enhanced-capabilities", "source": "project", "protocol": "execution", "name": "Enhanced Capabilities 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/enhanced-capabilities.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.973Z", "updatedAt": "2025-07-30T09:30:00.973Z", "scannedAt": "2025-07-30T09:30:00.973Z", "path": "role/daxiong/execution/enhanced-capabilities.execution.md"}}, {"id": "a-stock-expertise", "source": "project", "protocol": "knowledge", "name": "A Stock Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/a-stock-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.973Z", "updatedAt": "2025-07-30T09:30:00.973Z", "scannedAt": "2025-07-30T09:30:00.973Z", "path": "role/daxiong/knowledge/a-stock-expertise.knowledge.md"}}, {"id": "content-creation", "source": "project", "protocol": "knowledge", "name": "Content Creation 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/content-creation.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.974Z", "updatedAt": "2025-07-30T09:30:00.974Z", "scannedAt": "2025-07-30T09:30:00.974Z", "path": "role/daxiong/knowledge/content-creation.knowledge.md"}}, {"id": "daxiong-mindset", "source": "project", "protocol": "thought", "name": "Daxiong Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/daxiong/thought/daxiong-mindset.thought.md", "metadata": {"createdAt": "2025-07-30T09:30:00.975Z", "updatedAt": "2025-07-30T09:30:00.975Z", "scannedAt": "2025-07-30T09:30:00.975Z", "path": "role/daxiong/thought/daxiong-mindset.thought.md"}}, {"id": "python-cat-workflow", "source": "project", "protocol": "execution", "name": "Python Cat Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-cat-girl/execution/python-cat-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.975Z", "updatedAt": "2025-07-30T09:30:00.975Z", "scannedAt": "2025-07-30T09:30:00.975Z", "path": "role/python-cat-girl/execution/python-cat-workflow.execution.md"}}, {"id": "python-standards", "source": "project", "protocol": "execution", "name": "Python Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-cat-girl/execution/python-standards.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.976Z", "updatedAt": "2025-07-30T09:30:00.976Z", "scannedAt": "2025-07-30T09:30:00.976Z", "path": "role/python-cat-girl/execution/python-standards.execution.md"}}, {"id": "python-expertise", "source": "project", "protocol": "knowledge", "name": "Python Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/python-cat-girl/knowledge/python-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.976Z", "updatedAt": "2025-07-30T09:30:00.976Z", "scannedAt": "2025-07-30T09:30:00.976Z", "path": "role/python-cat-girl/knowledge/python-expertise.knowledge.md"}}, {"id": "python-cat-girl", "source": "project", "protocol": "role", "name": "Python Cat Girl 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-cat-girl/python-cat-girl.role.md", "metadata": {"createdAt": "2025-07-30T09:30:00.976Z", "updatedAt": "2025-07-30T09:30:00.976Z", "scannedAt": "2025-07-30T09:30:00.976Z", "path": "role/python-cat-girl/python-cat-girl.role.md"}}, {"id": "python-cat-thinking", "source": "project", "protocol": "thought", "name": "Python Cat Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-cat-girl/thought/python-cat-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:30:00.977Z", "updatedAt": "2025-07-30T09:30:00.977Z", "scannedAt": "2025-07-30T09:30:00.977Z", "path": "role/python-cat-girl/thought/python-cat-thinking.thought.md"}}, {"id": "html-standards", "source": "project", "protocol": "execution", "name": "Html Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-expert/execution/html-standards.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.978Z", "updatedAt": "2025-07-30T09:30:00.978Z", "scannedAt": "2025-07-30T09:30:00.978Z", "path": "role/web-expert/execution/html-standards.execution.md"}}, {"id": "web-design-workflow", "source": "project", "protocol": "execution", "name": "Web Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-expert/execution/web-design-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.978Z", "updatedAt": "2025-07-30T09:30:00.978Z", "scannedAt": "2025-07-30T09:30:00.978Z", "path": "role/web-expert/execution/web-design-workflow.execution.md"}}, {"id": "web-expertise", "source": "project", "protocol": "knowledge", "name": "Web Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/web-expert/knowledge/web-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.979Z", "updatedAt": "2025-07-30T09:30:00.979Z", "scannedAt": "2025-07-30T09:30:00.979Z", "path": "role/web-expert/knowledge/web-expertise.knowledge.md"}}, {"id": "web-design-thinking", "source": "project", "protocol": "thought", "name": "Web Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/web-expert/thought/web-design-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:30:00.979Z", "updatedAt": "2025-07-30T09:30:00.979Z", "scannedAt": "2025-07-30T09:30:00.979Z", "path": "role/web-expert/thought/web-design-thinking.thought.md"}}, {"id": "web-expert", "source": "project", "protocol": "role", "name": "Web Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/web-expert/web-expert.role.md", "metadata": {"createdAt": "2025-07-30T09:30:00.980Z", "updatedAt": "2025-07-30T09:30:00.980Z", "scannedAt": "2025-07-30T09:30:00.980Z", "path": "role/web-expert/web-expert.role.md"}}, {"id": "autonovels-core-prompts", "source": "project", "protocol": "execution", "name": "Autonovels Core Prompts 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/autonovels-core-prompts.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.980Z", "updatedAt": "2025-07-30T09:30:00.980Z", "scannedAt": "2025-07-30T09:30:00.980Z", "path": "role/xiongbao/execution/autonovels-core-prompts.execution.md"}}, {"id": "novel-writing-workflow", "source": "project", "protocol": "execution", "name": "Novel Writing Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/novel-writing-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.981Z", "updatedAt": "2025-07-30T09:30:00.981Z", "scannedAt": "2025-07-30T09:30:00.981Z", "path": "role/xiongbao/execution/novel-writing-workflow.execution.md"}}, {"id": "workspace-management", "source": "project", "protocol": "execution", "name": "Workspace Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/workspace-management.execution.md", "metadata": {"createdAt": "2025-07-30T09:30:00.982Z", "updatedAt": "2025-07-30T09:30:00.982Z", "scannedAt": "2025-07-30T09:30:00.982Z", "path": "role/xiongbao/execution/workspace-management.execution.md"}}, {"id": "autonovels-system", "source": "project", "protocol": "knowledge", "name": "Autonovels System 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/xiongbao/knowledge/autonovels-system.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.982Z", "updatedAt": "2025-07-30T09:30:00.982Z", "scannedAt": "2025-07-30T09:30:00.982Z", "path": "role/xiongbao/knowledge/autonovels-system.knowledge.md"}}, {"id": "xiongbao-workspace", "source": "project", "protocol": "knowledge", "name": "Xiongbao Workspace 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/xiongbao/knowledge/xiongbao-workspace.knowledge.md", "metadata": {"createdAt": "2025-07-30T09:30:00.983Z", "updatedAt": "2025-07-30T09:30:00.983Z", "scannedAt": "2025-07-30T09:30:00.983Z", "path": "role/xiongbao/knowledge/xiongbao-workspace.knowledge.md"}}, {"id": "autonovels-creation-thinking", "source": "project", "protocol": "thought", "name": "Autonovels Creation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/xiongbao/thought/autonovels-creation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:30:00.984Z", "updatedAt": "2025-07-30T09:30:00.984Z", "scannedAt": "2025-07-30T09:30:00.984Z", "path": "role/xiongbao/thought/autonovels-creation-thinking.thought.md"}}, {"id": "novel-creation-thinking", "source": "project", "protocol": "thought", "name": "Novel Creation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/xiongbao/thought/novel-creation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T09:30:00.984Z", "updatedAt": "2025-07-30T09:30:00.984Z", "scannedAt": "2025-07-30T09:30:00.984Z", "path": "role/xiongbao/thought/novel-creation-thinking.thought.md"}}, {"id": "xiongbao", "source": "project", "protocol": "role", "name": "Xiongbao 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/xiongbao/xiongbao.role.md", "metadata": {"createdAt": "2025-07-30T09:30:00.984Z", "updatedAt": "2025-07-30T09:30:00.984Z", "scannedAt": "2025-07-30T09:30:00.984Z", "path": "role/xiongbao/xiongbao.role.md"}}], "stats": {"totalResources": 29, "byProtocol": {"knowledge": 7, "role": 5, "execution": 11, "thought": 6}, "bySource": {"project": 29}}}