"""
🐾 用户模型
User Model

定义用户表结构和相关业务逻辑
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from passlib.context import CryptContext

from app.core.database import Base

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User(Base):
    """用户模型"""
    
    __tablename__ = "users"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="用户ID")
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=False, comment="邮箱")
    hashed_password = Column(String(255), nullable=False, comment="加密密码")
    
    # 个人信息
    nickname = Column(String(50), nullable=True, comment="昵称")
    avatar_url = Column(String(255), nullable=True, comment="头像URL")
    phone = Column(String(20), nullable=True, comment="手机号")
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    
    # 账户状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_verified = Column(Boolean, default=False, comment="是否验证")
    is_bot = Column(Boolean, default=False, comment="是否为机器人用户")
    
    # 资金信息
    balance = Column(Float, default=100000.0, comment="账户余额")
    frozen_balance = Column(Float, default=0.0, comment="冻结资金")
    total_assets = Column(Float, default=100000.0, comment="总资产")
    
    # 交易统计
    total_profit = Column(Float, default=0.0, comment="总盈亏")
    total_profit_rate = Column(Float, default=0.0, comment="总收益率")
    win_rate = Column(Float, default=0.0, comment="胜率")
    total_trades = Column(Integer, default=0, comment="总交易次数")
    
    # 风险控制
    risk_level = Column(String(20), default="medium", comment="风险等级: low/medium/high")
    max_position_ratio = Column(Float, default=0.3, comment="最大持仓比例")
    
    # 游戏化数据
    level = Column(Integer, default=1, comment="用户等级")
    experience = Column(Integer, default=0, comment="经验值")
    achievement_count = Column(Integer, default=0, comment="成就数量")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_login_at = Column(DateTime(timezone=True), nullable=True, comment="最后登录时间")
    
    # 其他信息
    bio = Column(Text, nullable=True, comment="个人简介")
    settings = Column(Text, nullable=True, comment="用户设置JSON")
    
    # 关系定义
    orders = relationship("Order", back_populates="user", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="user", cascade="all, delete-orphan")
    positions = relationship("Position", back_populates="user", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_user_email_active', 'email', 'is_active'),
        Index('idx_user_username_active', 'username', 'is_active'),
        Index('idx_user_created_at', 'created_at'),
        Index('idx_user_total_assets', 'total_assets'),
        Index('idx_user_is_bot', 'is_bot'),
    )
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @classmethod
    def create_password_hash(cls, password: str) -> str:
        """创建密码哈希"""
        return pwd_context.hash(password)
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.hashed_password)
    
    def set_password(self, password: str) -> None:
        """设置密码"""
        self.hashed_password = self.create_password_hash(password)
    
    @property
    def available_balance(self) -> float:
        """可用余额"""
        return self.balance - self.frozen_balance
    
    @property
    def market_value(self) -> float:
        """持仓市值"""
        return self.total_assets - self.balance
    
    @property
    def profit_rate(self) -> float:
        """收益率"""
        if self.total_assets <= 0:
            return 0.0
        return (self.total_assets - 100000.0) / 100000.0 * 100
    
    def update_assets(self) -> None:
        """更新总资产（余额 + 持仓市值）"""
        market_value = sum(pos.current_value for pos in self.positions if pos.quantity > 0)
        self.total_assets = self.balance + market_value
        self.total_profit = self.total_assets - 100000.0  # 初始资金10万
        self.total_profit_rate = self.profit_rate
    
    def freeze_balance(self, amount: float) -> bool:
        """冻结资金"""
        if self.available_balance >= amount:
            self.frozen_balance += amount
            return True
        return False
    
    def unfreeze_balance(self, amount: float) -> None:
        """解冻资金"""
        self.frozen_balance = max(0, self.frozen_balance - amount)
    
    def add_experience(self, exp: int) -> None:
        """增加经验值"""
        self.experience += exp
        # 简单的升级逻辑
        new_level = min(100, max(1, int(self.experience / 1000) + 1))
        if new_level > self.level:
            self.level = new_level
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "nickname": self.nickname,
            "avatar_url": self.avatar_url,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "is_bot": self.is_bot,
            "balance": self.balance,
            "frozen_balance": self.frozen_balance,
            "total_assets": self.total_assets,
            "total_profit": self.total_profit,
            "total_profit_rate": self.total_profit_rate,
            "win_rate": self.win_rate,
            "total_trades": self.total_trades,
            "risk_level": self.risk_level,
            "level": self.level,
            "experience": self.experience,
            "achievement_count": self.achievement_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
        }
