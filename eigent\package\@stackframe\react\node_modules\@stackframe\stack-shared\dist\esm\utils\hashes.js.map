{"version": 3, "sources": ["../../../src/utils/hashes.tsx"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { StackAssertionError } from './errors';\n\nexport async function sha512(input: Uint8Array | string): Promise<Uint8Array> {\n  const bytes = typeof input === \"string\" ? new TextEncoder().encode(input) : input;\n  return new Uint8Array(await crypto.subtle.digest(\"SHA-512\", bytes));\n}\n\nexport async function hashPassword(password: string) {\n  const passwordBytes = new TextEncoder().encode(password);\n  if (passwordBytes.length >= 72) {\n    throw new StackAssertionError(`Password is too long for bcrypt`, { len: passwordBytes.length });\n  }\n  const salt = await bcrypt.genSalt(10);\n  return await bcrypt.hash(password, salt);\n}\n\nexport async function comparePassword(password: string, hash: string): Promise<boolean> {\n  switch (await getPasswordHashAlgorithm(hash)) {\n    case \"bcrypt\": {\n      return await bcrypt.compare(password, hash);\n    }\n    default: {\n      return false;\n    }\n  }\n}\n\nexport async function isPasswordHashValid(hash: string) {\n  return !!(await getPasswordHashAlgorithm(hash));\n}\n\nexport async function getPasswordHashAlgorithm(hash: string): Promise<\"bcrypt\" | undefined> {\n  if (typeof hash !== \"string\") {\n    throw new StackAssertionError(`Passed non-string value to getPasswordHashAlgorithm`, { hash });\n  }\n  if (hash.match(/^\\$2[ayb]\\$.{56}$/)) {\n    try {\n      if (bcrypt.getRounds(hash) > 16) {\n        return undefined;\n      }\n      await bcrypt.compare(\"any string\", hash);\n      return \"bcrypt\";\n    } catch (e) {\n      console.warn(`Error while checking bcrypt password hash. Assuming the hash is invalid`, e);\n      return undefined;\n    }\n  } else {\n    return undefined;\n  }\n}\n"], "mappings": ";AAAA,OAAO,YAAY;AACnB,SAAS,2BAA2B;AAEpC,eAAsB,OAAO,OAAiD;AAC5E,QAAM,QAAQ,OAAO,UAAU,WAAW,IAAI,YAAY,EAAE,OAAO,KAAK,IAAI;AAC5E,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,OAAO,WAAW,KAAK,CAAC;AACpE;AAEA,eAAsB,aAAa,UAAkB;AACnD,QAAM,gBAAgB,IAAI,YAAY,EAAE,OAAO,QAAQ;AACvD,MAAI,cAAc,UAAU,IAAI;AAC9B,UAAM,IAAI,oBAAoB,mCAAmC,EAAE,KAAK,cAAc,OAAO,CAAC;AAAA,EAChG;AACA,QAAM,OAAO,MAAM,OAAO,QAAQ,EAAE;AACpC,SAAO,MAAM,OAAO,KAAK,UAAU,IAAI;AACzC;AAEA,eAAsB,gBAAgB,UAAkB,MAAgC;AACtF,UAAQ,MAAM,yBAAyB,IAAI,GAAG;AAAA,IAC5C,KAAK,UAAU;AACb,aAAO,MAAM,OAAO,QAAQ,UAAU,IAAI;AAAA,IAC5C;AAAA,IACA,SAAS;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,eAAsB,oBAAoB,MAAc;AACtD,SAAO,CAAC,CAAE,MAAM,yBAAyB,IAAI;AAC/C;AAEA,eAAsB,yBAAyB,MAA6C;AAC1F,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,IAAI,oBAAoB,uDAAuD,EAAE,KAAK,CAAC;AAAA,EAC/F;AACA,MAAI,KAAK,MAAM,mBAAmB,GAAG;AACnC,QAAI;AACF,UAAI,OAAO,UAAU,IAAI,IAAI,IAAI;AAC/B,eAAO;AAAA,MACT;AACA,YAAM,OAAO,QAAQ,cAAc,IAAI;AACvC,aAAO;AAAA,IACT,SAAS,GAAG;AACV,cAAQ,KAAK,2EAA2E,CAAC;AACzF,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;", "names": []}