{"version": 3, "sources": ["../../src/hooks/use-async-external-store.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\nimport { AsyncResult } from \"../utils/results\";\n\nexport function useAsyncExternalStore<T>(\n  subscribe: (callback: (t: T) => void) => (() => void),\n): AsyncResult<T, never> & { status: \"ok\" | \"pending\" } {\n  // sure, the \"sync\" in useSyncExternalStore refers to \"synchronize a store\" and not \"sync/async\", but it's too good of a name to pass up on\n\n  const [isAvailable, setIsAvailable] = useState(false);\n  const [value, setValue] = useState<T>();\n  useEffect(() => {\n    const unsubscribe = subscribe((value: T) => {\n      setValue(() => value);\n      setIsAvailable(() => true);\n    });\n    return unsubscribe;\n  }, [subscribe]);\n\n  if (isAvailable) {\n    return AsyncResult.ok(value as T);\n  } else {\n    return AsyncResult.pending();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAoC;AACpC,qBAA4B;AAErB,SAAS,sBACd,WACsD;AAGtD,QAAM,CAAC,aAAa,cAAc,QAAI,uBAAS,KAAK;AACpD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAY;AACtC,8BAAU,MAAM;AACd,UAAM,cAAc,UAAU,CAACA,WAAa;AAC1C,eAAS,MAAMA,MAAK;AACpB,qBAAe,MAAM,IAAI;AAAA,IAC3B,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,SAAS,CAAC;AAEd,MAAI,aAAa;AACf,WAAO,2BAAY,GAAG,KAAU;AAAA,EAClC,OAAO;AACL,WAAO,2BAAY,QAAQ;AAAA,EAC7B;AACF;", "names": ["value"]}