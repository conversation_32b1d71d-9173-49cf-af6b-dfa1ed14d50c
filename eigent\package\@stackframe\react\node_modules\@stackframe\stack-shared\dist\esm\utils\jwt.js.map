{"version": 3, "sources": ["../../../src/utils/jwt.tsx"], "sourcesContent": ["import crypto from \"crypto\";\nimport elliptic from \"elliptic\";\nimport * as jose from \"jose\";\nimport { JOSEError } from \"jose/errors\";\nimport { encodeBase64Url } from \"./bytes\";\nimport { StackAssertionError } from \"./errors\";\nimport { globalVar } from \"./globals\";\nimport { pick } from \"./objects\";\n\nconst STACK_SERVER_SECRET = process.env.STACK_SERVER_SECRET ?? \"\";\ntry {\n  jose.base64url.decode(STACK_SERVER_SECRET);\n} catch (e) {\n  throw new Error(\"STACK_SERVER_SECRET is not valid. Please use the generateKeys script to generate a new secret.\");\n}\n\n// TODO: remove this after moving everyone to project specific JWTs\nexport async function legacySignGlobalJWT(issuer: string, payload: any, expirationTime = \"5m\") {\n  const privateJwk = await jose.importJWK(await getPrivateJwk(STACK_SERVER_SECRET));\n  return await new jose.SignJWT(payload)\n    .setProtectedHeader({ alg: \"ES256\" })\n    .setIssuer(issuer)\n    .setIssuedAt()\n    .setExpirationTime(expirationTime)\n    .sign(privateJwk);\n}\n\n// TODO: remove this after moving everyone to project specific JWTs\nexport async function legacyVerifyGlobalJWT(issuer: string, jwt: string) {\n  const jwkSet = jose.createLocalJWKSet(await getPublicJwkSet(STACK_SERVER_SECRET));\n  const verified = await jose.jwtVerify(jwt, jwkSet, { issuer });\n  return verified.payload;\n}\n\nexport async function signJWT(options: {\n  issuer: string,\n  audience: string,\n  payload: any,\n  expirationTime?: string,\n}) {\n  const secret = getPerAudienceSecret({ audience: options.audience, secret: STACK_SERVER_SECRET });\n  const kid = getKid({ secret });\n  const privateJwk = await jose.importJWK(await getPrivateJwk(secret));\n  return await new jose.SignJWT(options.payload)\n    .setProtectedHeader({ alg: \"ES256\", kid })\n    .setIssuer(options.issuer)\n    .setIssuedAt()\n    .setAudience(options.audience)\n    .setExpirationTime(options.expirationTime || \"5m\")\n    .sign(privateJwk);\n}\n\nexport async function verifyJWT(options: {\n  issuer: string,\n  jwt: string,\n}) {\n  const audience = jose.decodeJwt(options.jwt).aud;\n  if (!audience || typeof audience !== \"string\") {\n    throw new JOSEError(\"Invalid JWT audience\");\n  }\n  const secret = getPerAudienceSecret({ audience, secret: STACK_SERVER_SECRET });\n  const jwkSet = jose.createLocalJWKSet(await getPublicJwkSet(secret));\n  const verified = await jose.jwtVerify(options.jwt, jwkSet, { issuer: options.issuer });\n  return verified.payload;\n}\n\nexport type PrivateJwk = {\n  kty: \"EC\",\n  alg: \"ES256\",\n  crv: \"P-256\",\n  kid: string,\n  d: string,\n  x: string,\n  y: string,\n};\nexport async function getPrivateJwk(secret: string): Promise<PrivateJwk> {\n  const secretHash = await globalVar.crypto.subtle.digest(\"SHA-256\", jose.base64url.decode(secret));\n  const priv = new Uint8Array(secretHash);\n\n  const ec = new elliptic.ec('p256');\n  const key = ec.keyFromPrivate(priv);\n  const publicKey = key.getPublic();\n\n  return {\n    kty: 'EC',\n    crv: 'P-256',\n    alg: 'ES256',\n    kid: getKid({ secret }),\n    d: encodeBase64Url(priv),\n    x: encodeBase64Url(publicKey.getX().toBuffer()),\n    y: encodeBase64Url(publicKey.getY().toBuffer()),\n  };\n}\n\nexport type PublicJwk = {\n  kty: \"EC\",\n  alg: \"ES256\",\n  crv: \"P-256\",\n  kid: string,\n  x: string,\n  y: string,\n};\nexport async function getPublicJwkSet(secretOrPrivateJwk: string | PrivateJwk): Promise<{ keys: PublicJwk[] }> {\n  const privateJwk = typeof secretOrPrivateJwk === \"string\" ? await getPrivateJwk(secretOrPrivateJwk) : secretOrPrivateJwk;\n  const jwk = pick(privateJwk, [\"kty\", \"alg\", \"crv\", \"x\", \"y\", \"kid\"]);\n  return {\n    keys: [jwk],\n  };\n}\n\nexport function getPerAudienceSecret(options: {\n  audience: string,\n  secret: string,\n}) {\n  if (options.audience === \"kid\") {\n    throw new StackAssertionError(\"You cannot use the 'kid' audience for a per-audience secret, see comment below in jwt.tsx\");\n  }\n  return jose.base64url.encode(\n    crypto\n      .createHash('sha256')\n      // TODO we should prefix a string like \"stack-audience-secret\" before we hash so you can't use `getKid(...)` to get the secret for eg. the \"kid\" audience if the same secret value is used\n      // Sadly doing this modification is a bit annoying as we need to leave the old keys to be valid for a little longer\n      .update(JSON.stringify([options.secret, options.audience]))\n      .digest()\n  );\n};\n\nexport function getKid(options: {\n  secret: string,\n}) {\n  return jose.base64url.encode(\n    crypto\n      .createHash('sha256')\n      .update(JSON.stringify([options.secret, \"kid\"]))  // TODO see above in getPerAudienceSecret\n      .digest()\n  ).slice(0, 12);\n}\n"], "mappings": ";AAAA,OAAO,YAAY;AACnB,OAAO,cAAc;AACrB,YAAY,UAAU;AACtB,SAAS,iBAAiB;AAC1B,SAAS,uBAAuB;AAChC,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAC1B,SAAS,YAAY;AAErB,IAAM,sBAAsB,QAAQ,IAAI,uBAAuB;AAC/D,IAAI;AACF,EAAK,eAAU,OAAO,mBAAmB;AAC3C,SAAS,GAAG;AACV,QAAM,IAAI,MAAM,gGAAgG;AAClH;AAGA,eAAsB,oBAAoB,QAAgB,SAAc,iBAAiB,MAAM;AAC7F,QAAM,aAAa,MAAW,eAAU,MAAM,cAAc,mBAAmB,CAAC;AAChF,SAAO,MAAM,IAAS,aAAQ,OAAO,EAClC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,EACnC,UAAU,MAAM,EAChB,YAAY,EACZ,kBAAkB,cAAc,EAChC,KAAK,UAAU;AACpB;AAGA,eAAsB,sBAAsB,QAAgB,KAAa;AACvE,QAAM,SAAc,uBAAkB,MAAM,gBAAgB,mBAAmB,CAAC;AAChF,QAAM,WAAW,MAAW,eAAU,KAAK,QAAQ,EAAE,OAAO,CAAC;AAC7D,SAAO,SAAS;AAClB;AAEA,eAAsB,QAAQ,SAK3B;AACD,QAAM,SAAS,qBAAqB,EAAE,UAAU,QAAQ,UAAU,QAAQ,oBAAoB,CAAC;AAC/F,QAAM,MAAM,OAAO,EAAE,OAAO,CAAC;AAC7B,QAAM,aAAa,MAAW,eAAU,MAAM,cAAc,MAAM,CAAC;AACnE,SAAO,MAAM,IAAS,aAAQ,QAAQ,OAAO,EAC1C,mBAAmB,EAAE,KAAK,SAAS,IAAI,CAAC,EACxC,UAAU,QAAQ,MAAM,EACxB,YAAY,EACZ,YAAY,QAAQ,QAAQ,EAC5B,kBAAkB,QAAQ,kBAAkB,IAAI,EAChD,KAAK,UAAU;AACpB;AAEA,eAAsB,UAAU,SAG7B;AACD,QAAM,WAAgB,eAAU,QAAQ,GAAG,EAAE;AAC7C,MAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7C,UAAM,IAAI,UAAU,sBAAsB;AAAA,EAC5C;AACA,QAAM,SAAS,qBAAqB,EAAE,UAAU,QAAQ,oBAAoB,CAAC;AAC7E,QAAM,SAAc,uBAAkB,MAAM,gBAAgB,MAAM,CAAC;AACnE,QAAM,WAAW,MAAW,eAAU,QAAQ,KAAK,QAAQ,EAAE,QAAQ,QAAQ,OAAO,CAAC;AACrF,SAAO,SAAS;AAClB;AAWA,eAAsB,cAAc,QAAqC;AACvE,QAAM,aAAa,MAAM,UAAU,OAAO,OAAO,OAAO,WAAgB,eAAU,OAAO,MAAM,CAAC;AAChG,QAAM,OAAO,IAAI,WAAW,UAAU;AAEtC,QAAM,KAAK,IAAI,SAAS,GAAG,MAAM;AACjC,QAAM,MAAM,GAAG,eAAe,IAAI;AAClC,QAAM,YAAY,IAAI,UAAU;AAEhC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,OAAO,EAAE,OAAO,CAAC;AAAA,IACtB,GAAG,gBAAgB,IAAI;AAAA,IACvB,GAAG,gBAAgB,UAAU,KAAK,EAAE,SAAS,CAAC;AAAA,IAC9C,GAAG,gBAAgB,UAAU,KAAK,EAAE,SAAS,CAAC;AAAA,EAChD;AACF;AAUA,eAAsB,gBAAgB,oBAAyE;AAC7G,QAAM,aAAa,OAAO,uBAAuB,WAAW,MAAM,cAAc,kBAAkB,IAAI;AACtG,QAAM,MAAM,KAAK,YAAY,CAAC,OAAO,OAAO,OAAO,KAAK,KAAK,KAAK,CAAC;AACnE,SAAO;AAAA,IACL,MAAM,CAAC,GAAG;AAAA,EACZ;AACF;AAEO,SAAS,qBAAqB,SAGlC;AACD,MAAI,QAAQ,aAAa,OAAO;AAC9B,UAAM,IAAI,oBAAoB,2FAA2F;AAAA,EAC3H;AACA,SAAY,eAAU;AAAA,IACpB,OACG,WAAW,QAAQ,EAGnB,OAAO,KAAK,UAAU,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,CAAC,CAAC,EACzD,OAAO;AAAA,EACZ;AACF;AAEO,SAAS,OAAO,SAEpB;AACD,SAAY,eAAU;AAAA,IACpB,OACG,WAAW,QAAQ,EACnB,OAAO,KAAK,UAAU,CAAC,QAAQ,QAAQ,KAAK,CAAC,CAAC,EAC9C,OAAO;AAAA,EACZ,EAAE,MAAM,GAAG,EAAE;AACf;", "names": []}