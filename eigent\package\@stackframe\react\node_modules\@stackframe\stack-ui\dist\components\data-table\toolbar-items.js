import { jsx as _jsx } from "react/jsx-runtime";
import { Input, cn } from "../..";
export function SearchToolbarItem(props) {
    return (_jsx(Input, { placeholder: props.placeholder, value: props.keyName ? `${props.table.getColumn(props.keyName)?.getFilterValue() ?? ""}` : props.table.getState().globalFilter ?? "", onChange: (event) => props.keyName ? props.table.getColumn(props.keyName)?.setFilterValue(event.target.value) : props.table.setGlobalFilter(event.target.value), className: cn("h-8 w-[250px]", props.className) }));
}
