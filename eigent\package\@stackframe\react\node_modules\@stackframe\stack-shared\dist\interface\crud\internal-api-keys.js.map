{"version": 3, "sources": ["../../../src/interface/crud/internal-api-keys.ts"], "sourcesContent": ["import { CrudTypeOf, createCrud } from \"../../crud\";\nimport { yupBoolean, yupMixed, yupNumber, yupObject, yupString } from \"../../schema-fields\";\n\nconst baseInternalApiKeysReadSchema = yupObject({\n  id: yupString().defined(),\n  description: yupString().defined(),\n  expires_at_millis: yupNumber().defined(),\n  manually_revoked_at_millis: yupNumber().optional(),\n  created_at_millis: yupNumber().defined(),\n});\n\n// Used for the result of the create endpoint\nexport const internalApiKeysCreateInputSchema = yupObject({\n  description: yupString().defined(),\n  expires_at_millis: yupNumber().defined(),\n  has_publishable_client_key: yupBoolean().defined(),\n  has_secret_server_key: yupBoolean().defined(),\n  has_super_secret_admin_key: yupBoolean().defined(),\n});\n\nexport const internalApiKeysCreateOutputSchema = baseInternalApiKeysReadSchema.concat(yupObject({\n  publishable_client_key: yupString().optional(),\n  secret_server_key: yupString().optional(),\n  super_secret_admin_key: yupString().optional(),\n}).defined());\n\n// Used for list, read and update endpoints after the initial creation\nexport const internalApiKeysCrudAdminObfuscatedReadSchema = baseInternalApiKeysReadSchema.concat(yupObject({\n  publishable_client_key: yupObject({\n    last_four: yupString().defined(),\n  }).optional(),\n  secret_server_key: yupObject({\n    last_four: yupString().defined(),\n  }).optional(),\n  super_secret_admin_key: yupObject({\n    last_four: yupString().defined(),\n  }).optional(),\n}));\n\nexport const internalApiKeysCrudAdminUpdateSchema = yupObject({\n  description: yupString().optional(),\n  revoked: yupBoolean().oneOf([true]).optional(),\n}).defined();\n\nexport const internalApiKeysCrudAdminDeleteSchema = yupMixed();\n\nexport const internalApiKeysCrud = createCrud({\n  adminReadSchema: internalApiKeysCrudAdminObfuscatedReadSchema,\n  adminUpdateSchema: internalApiKeysCrudAdminUpdateSchema,\n  adminDeleteSchema: internalApiKeysCrudAdminDeleteSchema,\n  docs: {\n    adminList: {\n      hidden: true,\n    },\n    adminRead: {\n      hidden: true,\n    },\n    adminCreate: {\n      hidden: true,\n    },\n    adminUpdate: {\n      hidden: true,\n    },\n    adminDelete: {\n      hidden: true,\n    },\n  },\n});\nexport type InternalApiKeysCrud = CrudTypeOf<typeof internalApiKeysCrud>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAuC;AACvC,2BAAsE;AAEtE,IAAM,oCAAgC,gCAAU;AAAA,EAC9C,QAAI,gCAAU,EAAE,QAAQ;AAAA,EACxB,iBAAa,gCAAU,EAAE,QAAQ;AAAA,EACjC,uBAAmB,gCAAU,EAAE,QAAQ;AAAA,EACvC,gCAA4B,gCAAU,EAAE,SAAS;AAAA,EACjD,uBAAmB,gCAAU,EAAE,QAAQ;AACzC,CAAC;AAGM,IAAM,uCAAmC,gCAAU;AAAA,EACxD,iBAAa,gCAAU,EAAE,QAAQ;AAAA,EACjC,uBAAmB,gCAAU,EAAE,QAAQ;AAAA,EACvC,gCAA4B,iCAAW,EAAE,QAAQ;AAAA,EACjD,2BAAuB,iCAAW,EAAE,QAAQ;AAAA,EAC5C,gCAA4B,iCAAW,EAAE,QAAQ;AACnD,CAAC;AAEM,IAAM,oCAAoC,8BAA8B,WAAO,gCAAU;AAAA,EAC9F,4BAAwB,gCAAU,EAAE,SAAS;AAAA,EAC7C,uBAAmB,gCAAU,EAAE,SAAS;AAAA,EACxC,4BAAwB,gCAAU,EAAE,SAAS;AAC/C,CAAC,EAAE,QAAQ,CAAC;AAGL,IAAM,+CAA+C,8BAA8B,WAAO,gCAAU;AAAA,EACzG,4BAAwB,gCAAU;AAAA,IAChC,eAAW,gCAAU,EAAE,QAAQ;AAAA,EACjC,CAAC,EAAE,SAAS;AAAA,EACZ,uBAAmB,gCAAU;AAAA,IAC3B,eAAW,gCAAU,EAAE,QAAQ;AAAA,EACjC,CAAC,EAAE,SAAS;AAAA,EACZ,4BAAwB,gCAAU;AAAA,IAChC,eAAW,gCAAU,EAAE,QAAQ;AAAA,EACjC,CAAC,EAAE,SAAS;AACd,CAAC,CAAC;AAEK,IAAM,2CAAuC,gCAAU;AAAA,EAC5D,iBAAa,gCAAU,EAAE,SAAS;AAAA,EAClC,aAAS,iCAAW,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS;AAC/C,CAAC,EAAE,QAAQ;AAEJ,IAAM,2CAAuC,+BAAS;AAEtD,IAAM,0BAAsB,wBAAW;AAAA,EAC5C,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,MAAM;AAAA,IACJ,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,IACA,aAAa;AAAA,MACX,QAAQ;AAAA,IACV;AAAA,EACF;AACF,CAAC;", "names": []}