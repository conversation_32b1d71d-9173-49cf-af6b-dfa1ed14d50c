{"version": 3, "sources": ["../../../src/utils/caches.tsx"], "sourcesContent": ["import { DependenciesMap } from \"./maps\";\nimport { filterUndefined } from \"./objects\";\nimport { RateLimitOptions, ReactPromise, pending, rateLimited, resolved, runAsynchronously, wait } from \"./promises\";\nimport { AsyncStore } from \"./stores\";\n\n/**\n * Can be used to cache the result of a function call, for example for the `use` hook in React.\n */\nexport function cacheFunction<F extends Function>(f: F): F {\n  const dependenciesMap = new DependenciesMap<any, any>();\n\n  return ((...args: any) => {\n    if (dependenciesMap.has(args)) {\n      return dependenciesMap.get(args);\n    }\n\n    const value = f(...args);\n    dependenciesMap.set(args, value);\n    return value;\n  }) as any as F;\n}\nundefined?.test(\"cacheFunction\", ({ expect }) => {\n  // Test with a simple function\n  let callCount = 0;\n  const add = (a: number, b: number) => {\n    callCount++;\n    return a + b;\n  };\n\n  const cachedAdd = cacheFunction(add);\n\n  // First call should execute the function\n  expect(cachedAdd(1, 2)).toBe(3);\n  expect(callCount).toBe(1);\n\n  // Second call with same args should use cached result\n  expect(cachedAdd(1, 2)).toBe(3);\n  expect(callCount).toBe(1);\n\n  // Call with different args should execute the function again\n  expect(cachedAdd(2, 3)).toBe(5);\n  expect(callCount).toBe(2);\n\n  // Test with a function that returns objects\n  let objectCallCount = 0;\n  const createObject = (id: number) => {\n    objectCallCount++;\n    return { id };\n  };\n\n  const cachedCreateObject = cacheFunction(createObject);\n\n  // First call should execute the function\n  const obj1 = cachedCreateObject(1);\n  expect(obj1).toEqual({ id: 1 });\n  expect(objectCallCount).toBe(1);\n\n  // Second call with same args should use cached result\n  const obj2 = cachedCreateObject(1);\n  expect(obj2).toBe(obj1); // Same reference\n  expect(objectCallCount).toBe(1);\n});\n\n\ntype CacheStrategy = \"write-only\" | \"read-write\" | \"never\";\n\nexport class AsyncCache<D extends any[], T> {\n  private readonly _map = new DependenciesMap<D, AsyncValueCache<T>>();\n\n  constructor(\n    private readonly _fetcher: (dependencies: D) => Promise<T>,\n    private readonly _options: {\n      onSubscribe?: (key: D, refresh: () => void) => (() => void),\n      rateLimiter?: Omit<RateLimitOptions, \"batchCalls\">,\n    } = {},\n  ) {\n    // nothing here yet\n  }\n\n  private _createKeyed<FunctionName extends keyof AsyncValueCache<T>>(\n    functionName: FunctionName,\n  ): (key: D, ...args: Parameters<AsyncValueCache<T>[FunctionName]>) => ReturnType<AsyncValueCache<T>[FunctionName]> {\n    return (key: D, ...args) => {\n      const valueCache = this.getValueCache(key);\n      return (valueCache[functionName] as any).apply(valueCache, args);\n    };\n  }\n\n  getValueCache(dependencies: D): AsyncValueCache<T> {\n    let cache = this._map.get(dependencies);\n    if (!cache) {\n      cache = new AsyncValueCache(\n        async () => await this._fetcher(dependencies),\n        {\n          ...this._options,\n          onSubscribe: this._options.onSubscribe ? (cb) => this._options.onSubscribe!(dependencies, cb) : undefined,\n        },\n      );\n      this._map.set(dependencies, cache);\n    }\n    return cache;\n  }\n\n  async refreshWhere(predicate: (dependencies: D) => boolean) {\n    const promises: Promise<T>[] = [];\n    for (const [dependencies, cache] of this._map) {\n      if (predicate(dependencies)) {\n        promises.push(cache.refresh());\n      }\n    }\n    await Promise.all(promises);\n  }\n\n  readonly isCacheAvailable = this._createKeyed(\"isCacheAvailable\");\n  readonly getIfCached = this._createKeyed(\"getIfCached\");\n  readonly getOrWait = this._createKeyed(\"getOrWait\");\n  readonly forceSetCachedValue = this._createKeyed(\"forceSetCachedValue\");\n  readonly forceSetCachedValueAsync = this._createKeyed(\"forceSetCachedValueAsync\");\n  readonly refresh = this._createKeyed(\"refresh\");\n  readonly invalidate = this._createKeyed(\"invalidate\");\n  readonly onStateChange = this._createKeyed(\"onStateChange\");\n}\n\nclass AsyncValueCache<T> {\n  private _store: AsyncStore<T>;\n  private _pendingPromise: ReactPromise<T> | undefined;\n  private _fetcher: () => Promise<T>;\n  private readonly _rateLimitOptions: Omit<RateLimitOptions, \"batchCalls\">;\n  private _subscriptionsCount = 0;\n  private _unsubscribers: (() => void)[] = [];\n  private _mostRecentRefreshPromiseIndex = 0;\n\n  constructor(\n    fetcher: () => Promise<T>,\n    private readonly _options: {\n      onSubscribe?: (refresh: () => void) => (() => void),\n      rateLimiter?: Omit<RateLimitOptions, \"batchCalls\">,\n    } = {},\n  ) {\n    this._store = new AsyncStore();\n    this._rateLimitOptions = {\n      concurrency: 1,\n      throttleMs: 300,\n      ...filterUndefined(_options.rateLimiter ?? {}),\n    };\n\n\n    this._fetcher = rateLimited(fetcher, {\n      ...this._rateLimitOptions,\n      batchCalls: true,\n    });\n  }\n\n  isCacheAvailable(): boolean {\n    return this._store.isAvailable();\n  }\n\n  getIfCached() {\n    return this._store.get();\n  }\n\n  getOrWait(cacheStrategy: CacheStrategy): ReactPromise<T> {\n    const cached = this.getIfCached();\n    if (cacheStrategy === \"read-write\" && cached.status === \"ok\") {\n      return resolved(cached.data);\n    }\n\n    return this._refetch(cacheStrategy);\n  }\n\n  private _set(value: T): void {\n    this._store.set(value);\n  }\n\n  private _setAsync(value: Promise<T>): ReactPromise<boolean> {\n    const promise = pending(value);\n    this._pendingPromise = promise;\n    return pending(this._store.setAsync(promise));\n  }\n\n  private _refetch(cacheStrategy: CacheStrategy): ReactPromise<T> {\n    if (cacheStrategy === \"read-write\" && this._pendingPromise) {\n      return this._pendingPromise;\n    }\n    const promise = pending(this._fetcher());\n    if (cacheStrategy === \"never\") {\n      return promise;\n    }\n    return pending(this._setAsync(promise).then(() => promise));\n  }\n\n  forceSetCachedValue(value: T): void {\n    this._set(value);\n  }\n\n  forceSetCachedValueAsync(value: Promise<T>): ReactPromise<boolean> {\n    return this._setAsync(value);\n  }\n\n  /**\n   * Refetches the value from the fetcher, and updates the cache with it.\n   */\n  async refresh(): Promise<T> {\n    return await this.getOrWait(\"write-only\");\n  }\n\n  /**\n   * Invalidates the cache, marking it to refresh on the next read. If anyone was listening to it, it will refresh\n   * immediately.\n   */\n  invalidate(): void {\n    this._store.setUnavailable();\n    this._pendingPromise = undefined;\n    if (this._subscriptionsCount > 0) {\n      runAsynchronously(this.refresh());\n    }\n  }\n\n  onStateChange(callback: (value: T, oldValue: T | undefined) => void): { unsubscribe: () => void } {\n    const storeObj = this._store.onChange(callback);\n\n    runAsynchronously(this.getOrWait(\"read-write\"));\n\n    if (this._subscriptionsCount++ === 0 && this._options.onSubscribe) {\n      const unsubscribe = this._options.onSubscribe(() => {\n        runAsynchronously(this.refresh());\n      });\n      this._unsubscribers.push(unsubscribe);\n    }\n\n    let hasUnsubscribed = false;\n    return {\n      unsubscribe: () => {\n        if (hasUnsubscribed) return;\n        hasUnsubscribed = true;\n        storeObj.unsubscribe();\n        if (--this._subscriptionsCount === 0) {\n          const currentRefreshPromiseIndex = ++this._mostRecentRefreshPromiseIndex;\n          runAsynchronously(async () => {\n            // wait a few seconds; if anything changes during that time, we don't want to refresh\n            // else we do unnecessary requests if we unsubscribe and then subscribe again immediately\n            await wait(5000);\n            if (this._subscriptionsCount === 0 && currentRefreshPromiseIndex === this._mostRecentRefreshPromiseIndex) {\n              this.invalidate();\n            }\n          });\n\n          for (const unsubscribe of this._unsubscribers) {\n            unsubscribe();\n          }\n        }\n      },\n    };\n  }\n}\n"], "mappings": ";AAAA,SAAS,uBAAuB;AAChC,SAAS,uBAAuB;AAChC,SAAyC,SAAS,aAAa,UAAU,mBAAmB,YAAY;AACxG,SAAS,kBAAkB;AAKpB,SAAS,cAAkC,GAAS;AACzD,QAAM,kBAAkB,IAAI,gBAA0B;AAEtD,SAAQ,IAAI,SAAc;AACxB,QAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,aAAO,gBAAgB,IAAI,IAAI;AAAA,IACjC;AAEA,UAAM,QAAQ,EAAE,GAAG,IAAI;AACvB,oBAAgB,IAAI,MAAM,KAAK;AAC/B,WAAO;AAAA,EACT;AACF;AA8CO,IAAM,aAAN,MAAqC;AAAA,EAG1C,YACmB,UACA,WAGb,CAAC,GACL;AALiB;AACA;AAJnB,SAAiB,OAAO,IAAI,gBAAuC;AA8CnE,SAAS,mBAAmB,KAAK,aAAa,kBAAkB;AAChE,SAAS,cAAc,KAAK,aAAa,aAAa;AACtD,SAAS,YAAY,KAAK,aAAa,WAAW;AAClD,SAAS,sBAAsB,KAAK,aAAa,qBAAqB;AACtE,SAAS,2BAA2B,KAAK,aAAa,0BAA0B;AAChF,SAAS,UAAU,KAAK,aAAa,SAAS;AAC9C,SAAS,aAAa,KAAK,aAAa,YAAY;AACpD,SAAS,gBAAgB,KAAK,aAAa,eAAe;AAAA,EA3C1D;AAAA,EAEQ,aACN,cACiH;AACjH,WAAO,CAAC,QAAW,SAAS;AAC1B,YAAM,aAAa,KAAK,cAAc,GAAG;AACzC,aAAQ,WAAW,YAAY,EAAU,MAAM,YAAY,IAAI;AAAA,IACjE;AAAA,EACF;AAAA,EAEA,cAAc,cAAqC;AACjD,QAAI,QAAQ,KAAK,KAAK,IAAI,YAAY;AACtC,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI;AAAA,QACV,YAAY,MAAM,KAAK,SAAS,YAAY;AAAA,QAC5C;AAAA,UACE,GAAG,KAAK;AAAA,UACR,aAAa,KAAK,SAAS,cAAc,CAAC,OAAO,KAAK,SAAS,YAAa,cAAc,EAAE,IAAI;AAAA,QAClG;AAAA,MACF;AACA,WAAK,KAAK,IAAI,cAAc,KAAK;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,aAAa,WAAyC;AAC1D,UAAM,WAAyB,CAAC;AAChC,eAAW,CAAC,cAAc,KAAK,KAAK,KAAK,MAAM;AAC7C,UAAI,UAAU,YAAY,GAAG;AAC3B,iBAAS,KAAK,MAAM,QAAQ,CAAC;AAAA,MAC/B;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AAUF;AAEA,IAAM,kBAAN,MAAyB;AAAA,EASvB,YACE,SACiB,WAGb,CAAC,GACL;AAJiB;AANnB,SAAQ,sBAAsB;AAC9B,SAAQ,iBAAiC,CAAC;AAC1C,SAAQ,iCAAiC;AASvC,SAAK,SAAS,IAAI,WAAW;AAC7B,SAAK,oBAAoB;AAAA,MACvB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,GAAG,gBAAgB,SAAS,eAAe,CAAC,CAAC;AAAA,IAC/C;AAGA,SAAK,WAAW,YAAY,SAAS;AAAA,MACnC,GAAG,KAAK;AAAA,MACR,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,mBAA4B;AAC1B,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA,EAEA,UAAU,eAA+C;AACvD,UAAM,SAAS,KAAK,YAAY;AAChC,QAAI,kBAAkB,gBAAgB,OAAO,WAAW,MAAM;AAC5D,aAAO,SAAS,OAAO,IAAI;AAAA,IAC7B;AAEA,WAAO,KAAK,SAAS,aAAa;AAAA,EACpC;AAAA,EAEQ,KAAK,OAAgB;AAC3B,SAAK,OAAO,IAAI,KAAK;AAAA,EACvB;AAAA,EAEQ,UAAU,OAA0C;AAC1D,UAAM,UAAU,QAAQ,KAAK;AAC7B,SAAK,kBAAkB;AACvB,WAAO,QAAQ,KAAK,OAAO,SAAS,OAAO,CAAC;AAAA,EAC9C;AAAA,EAEQ,SAAS,eAA+C;AAC9D,QAAI,kBAAkB,gBAAgB,KAAK,iBAAiB;AAC1D,aAAO,KAAK;AAAA,IACd;AACA,UAAM,UAAU,QAAQ,KAAK,SAAS,CAAC;AACvC,QAAI,kBAAkB,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,KAAK,UAAU,OAAO,EAAE,KAAK,MAAM,OAAO,CAAC;AAAA,EAC5D;AAAA,EAEA,oBAAoB,OAAgB;AAClC,SAAK,KAAK,KAAK;AAAA,EACjB;AAAA,EAEA,yBAAyB,OAA0C;AACjE,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAsB;AAC1B,WAAO,MAAM,KAAK,UAAU,YAAY;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAmB;AACjB,SAAK,OAAO,eAAe;AAC3B,SAAK,kBAAkB;AACvB,QAAI,KAAK,sBAAsB,GAAG;AAChC,wBAAkB,KAAK,QAAQ,CAAC;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,cAAc,UAAoF;AAChG,UAAM,WAAW,KAAK,OAAO,SAAS,QAAQ;AAE9C,sBAAkB,KAAK,UAAU,YAAY,CAAC;AAE9C,QAAI,KAAK,0BAA0B,KAAK,KAAK,SAAS,aAAa;AACjE,YAAM,cAAc,KAAK,SAAS,YAAY,MAAM;AAClD,0BAAkB,KAAK,QAAQ,CAAC;AAAA,MAClC,CAAC;AACD,WAAK,eAAe,KAAK,WAAW;AAAA,IACtC;AAEA,QAAI,kBAAkB;AACtB,WAAO;AAAA,MACL,aAAa,MAAM;AACjB,YAAI,gBAAiB;AACrB,0BAAkB;AAClB,iBAAS,YAAY;AACrB,YAAI,EAAE,KAAK,wBAAwB,GAAG;AACpC,gBAAM,6BAA6B,EAAE,KAAK;AAC1C,4BAAkB,YAAY;AAG5B,kBAAM,KAAK,GAAI;AACf,gBAAI,KAAK,wBAAwB,KAAK,+BAA+B,KAAK,gCAAgC;AACxG,mBAAK,WAAW;AAAA,YAClB;AAAA,UACF,CAAC;AAED,qBAAW,eAAe,KAAK,gBAAgB;AAC7C,wBAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}