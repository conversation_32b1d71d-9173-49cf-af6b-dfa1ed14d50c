{"version": 3, "sources": ["../../src/utils/compile-time.tsx"], "sourcesContent": ["/**\n * Returns the first argument passed to it, but compilers won't be able to optimize it out. This is useful in some\n * cases where compiler warnings go awry; for example, when importing things that may not exist (but are guaranteed\n * to exist at runtime).\n */\nexport function scrambleDuringCompileTime<T>(t: T): T {\n  if (Math.random() < 0.00001 && Math.random() > 0.99999 && Math.random() < 0.00001 && Math.random() > 0.99999) {\n    return \"this will never happen\" as any;\n  }\n  return t;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKO,SAAS,0BAA6B,GAAS;AACpD,MAAI,KAAK,OAAO,IAAI,QAAW,KAAK,OAAO,IAAI,WAAW,KAAK,OAAO,IAAI,QAAW,KAAK,OAAO,IAAI,SAAS;AAC5G,WAAO;AAAA,EACT;AACA,SAAO;AACT;", "names": []}