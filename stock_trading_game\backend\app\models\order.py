"""
🐾 订单模型
Order Model

定义交易订单表结构和相关业务逻辑
"""

from datetime import datetime
from enum import Enum
from typing import Optional
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Index, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class OrderType(str, Enum):
    """订单类型"""
    BUY = "buy"          # 买入
    SELL = "sell"        # 卖出


class OrderPriceType(str, Enum):
    """订单价格类型"""
    MARKET = "market"    # 市价单
    LIMIT = "limit"      # 限价单
    STOP = "stop"        # 止损单
    STOP_LIMIT = "stop_limit"  # 止损限价单


class OrderStatus(str, Enum):
    """订单状态"""
    PENDING = "pending"          # 待成交
    PARTIAL_FILLED = "partial_filled"  # 部分成交
    FILLED = "filled"            # 已成交
    CANCELLED = "cancelled"      # 已取消
    REJECTED = "rejected"        # 已拒绝
    EXPIRED = "expired"          # 已过期


class Order(Base):
    """订单模型"""
    
    __tablename__ = "orders"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="订单ID")
    order_no = Column(String(32), unique=True, index=True, nullable=False, comment="订单编号")
    
    # 关联字段
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, comment="股票ID")
    
    # 订单信息
    order_type = Column(SQLEnum(OrderType), nullable=False, comment="订单类型")
    price_type = Column(SQLEnum(OrderPriceType), nullable=False, comment="价格类型")
    status = Column(SQLEnum(OrderStatus), default=OrderStatus.PENDING, comment="订单状态")
    
    # 价格和数量
    price = Column(Float, nullable=True, comment="委托价格")
    quantity = Column(Integer, nullable=False, comment="委托数量")
    filled_quantity = Column(Integer, default=0, comment="已成交数量")
    avg_filled_price = Column(Float, nullable=True, comment="平均成交价格")
    
    # 止损相关
    stop_price = Column(Float, nullable=True, comment="止损价格")
    trigger_price = Column(Float, nullable=True, comment="触发价格")
    
    # 金额计算
    total_amount = Column(Float, nullable=False, comment="委托总金额")
    filled_amount = Column(Float, default=0.0, comment="已成交金额")
    fee = Column(Float, default=0.0, comment="手续费")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    filled_at = Column(DateTime(timezone=True), nullable=True, comment="成交时间")
    cancelled_at = Column(DateTime(timezone=True), nullable=True, comment="取消时间")
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")
    
    # 其他信息
    notes = Column(String(500), nullable=True, comment="备注")
    source = Column(String(50), default="web", comment="订单来源")
    
    # 关系定义
    user = relationship("User", back_populates="orders")
    stock = relationship("Stock", back_populates="orders")
    transactions = relationship("Transaction", back_populates="order")
    
    # 索引
    __table_args__ = (
        Index('idx_order_user_id', 'user_id'),
        Index('idx_order_stock_id', 'stock_id'),
        Index('idx_order_status', 'status'),
        Index('idx_order_type', 'order_type'),
        Index('idx_order_created_at', 'created_at'),
        Index('idx_order_user_status', 'user_id', 'status'),
        Index('idx_order_stock_status', 'stock_id', 'status'),
    )
    
    def __repr__(self):
        return f"<Order(id={self.id}, order_no='{self.order_no}', type={self.order_type}, status={self.status})>"
    
    @property
    def remaining_quantity(self) -> int:
        """剩余数量"""
        return self.quantity - self.filled_quantity
    
    @property
    def fill_rate(self) -> float:
        """成交率"""
        if self.quantity == 0:
            return 0.0
        return self.filled_quantity / self.quantity
    
    @property
    def is_active(self) -> bool:
        """是否为活跃订单"""
        return self.status in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED]
    
    @property
    def can_cancel(self) -> bool:
        """是否可以取消"""
        return self.status in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]
    
    def calculate_total_amount(self, stock_price: float = None) -> float:
        """计算委托总金额"""
        if self.price_type == OrderPriceType.MARKET:
            # 市价单使用当前股价估算
            price = stock_price or self.price or 0
        else:
            price = self.price or 0
        
        return price * self.quantity
    
    def calculate_fee(self, amount: float) -> float:
        """计算手续费"""
        from app.core.config import settings
        return amount * settings.TRANSACTION_FEE_RATE
    
    def partial_fill(self, filled_qty: int, filled_price: float) -> None:
        """部分成交"""
        if filled_qty <= 0 or filled_qty > self.remaining_quantity:
            return
        
        # 更新成交数量和金额
        old_filled_amount = self.filled_amount
        new_filled_amount = filled_qty * filled_price
        
        self.filled_quantity += filled_qty
        self.filled_amount += new_filled_amount
        
        # 计算平均成交价格
        total_filled_amount = old_filled_amount + new_filled_amount
        if self.filled_quantity > 0:
            self.avg_filled_price = total_filled_amount / self.filled_quantity
        
        # 更新状态
        if self.filled_quantity >= self.quantity:
            self.status = OrderStatus.FILLED
            self.filled_at = datetime.utcnow()
        else:
            self.status = OrderStatus.PARTIAL_FILLED
        
        # 计算手续费
        self.fee += self.calculate_fee(new_filled_amount)
    
    def cancel(self, reason: str = None) -> bool:
        """取消订单"""
        if not self.can_cancel:
            return False
        
        self.status = OrderStatus.CANCELLED
        self.cancelled_at = datetime.utcnow()
        if reason:
            self.notes = f"{self.notes or ''} 取消原因: {reason}".strip()
        
        return True
    
    def reject(self, reason: str = None) -> None:
        """拒绝订单"""
        self.status = OrderStatus.REJECTED
        if reason:
            self.notes = f"{self.notes or ''} 拒绝原因: {reason}".strip()
    
    def expire(self) -> None:
        """订单过期"""
        if self.is_active:
            self.status = OrderStatus.EXPIRED
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "order_no": self.order_no,
            "user_id": self.user_id,
            "stock_id": self.stock_id,
            "order_type": self.order_type.value,
            "price_type": self.price_type.value,
            "status": self.status.value,
            "price": self.price,
            "quantity": self.quantity,
            "filled_quantity": self.filled_quantity,
            "remaining_quantity": self.remaining_quantity,
            "avg_filled_price": self.avg_filled_price,
            "fill_rate": self.fill_rate,
            "stop_price": self.stop_price,
            "trigger_price": self.trigger_price,
            "total_amount": self.total_amount,
            "filled_amount": self.filled_amount,
            "fee": self.fee,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "filled_at": self.filled_at.isoformat() if self.filled_at else None,
            "cancelled_at": self.cancelled_at.isoformat() if self.cancelled_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "notes": self.notes,
            "source": self.source,
            "is_active": self.is_active,
            "can_cancel": self.can_cancel,
        }
