"""
🐾 股票模型
Stock Model

定义股票表结构和相关业务逻辑
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class Stock(Base):
    """股票模型"""
    
    __tablename__ = "stocks"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="股票ID")
    code = Column(String(10), unique=True, index=True, nullable=False, comment="股票代码")
    name = Column(String(50), nullable=False, comment="股票名称")
    industry = Column(String(50), nullable=True, comment="所属行业")
    
    # 价格信息
    current_price = Column(Float, nullable=False, comment="当前价格")
    opening_price = Column(Float, nullable=False, comment="开盘价")
    closing_price = Column(Float, nullable=False, comment="收盘价")
    highest_price = Column(Float, nullable=False, comment="最高价")
    lowest_price = Column(Float, nullable=False, comment="最低价")
    
    # 昨日数据
    prev_closing_price = Column(Float, nullable=True, comment="昨日收盘价")
    
    # 交易数据
    volume = Column(Integer, default=0, comment="成交量")
    turnover = Column(Float, default=0.0, comment="成交额")
    
    # 市场数据
    market_cap = Column(Float, nullable=True, comment="总市值")
    circulating_market_cap = Column(Float, nullable=True, comment="流通市值")
    pe_ratio = Column(Float, nullable=True, comment="市盈率")
    pb_ratio = Column(Float, nullable=True, comment="市净率")
    
    # 技术指标
    ma5 = Column(Float, nullable=True, comment="5日均线")
    ma10 = Column(Float, nullable=True, comment="10日均线")
    ma20 = Column(Float, nullable=True, comment="20日均线")
    ma60 = Column(Float, nullable=True, comment="60日均线")
    
    # 波动率和风险指标
    volatility = Column(Float, default=0.02, comment="波动率")
    beta = Column(Float, default=1.0, comment="贝塔系数")
    
    # 市场情绪
    sentiment_score = Column(Float, default=0.0, comment="市场情绪分数 (-1到1)")
    news_impact = Column(Float, default=0.0, comment="新闻影响因子")
    
    # 交易状态
    is_active = Column(Boolean, default=True, comment="是否可交易")
    is_suspended = Column(Boolean, default=False, comment="是否停牌")
    is_st = Column(Boolean, default=False, comment="是否ST股票")
    
    # 涨跌停价格
    limit_up_price = Column(Float, nullable=True, comment="涨停价")
    limit_down_price = Column(Float, nullable=True, comment="跌停价")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_trade_time = Column(DateTime(timezone=True), nullable=True, comment="最后交易时间")
    
    # 其他信息
    description = Column(Text, nullable=True, comment="股票描述")
    tags = Column(String(200), nullable=True, comment="标签")
    
    # 关系定义
    orders = relationship("Order", back_populates="stock")
    transactions = relationship("Transaction", back_populates="stock")
    positions = relationship("Position", back_populates="stock")
    market_data = relationship("MarketData", back_populates="stock", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_stock_code_active', 'code', 'is_active'),
        Index('idx_stock_industry', 'industry'),
        Index('idx_stock_current_price', 'current_price'),
        Index('idx_stock_market_cap', 'market_cap'),
        Index('idx_stock_volume', 'volume'),
        Index('idx_stock_updated_at', 'updated_at'),
    )
    
    def __repr__(self):
        return f"<Stock(id={self.id}, code='{self.code}', name='{self.name}', price={self.current_price})>"
    
    @property
    def change_amount(self) -> float:
        """涨跌金额"""
        if self.prev_closing_price:
            return self.current_price - self.prev_closing_price
        return 0.0
    
    @property
    def change_percent(self) -> float:
        """涨跌幅百分比"""
        if self.prev_closing_price and self.prev_closing_price > 0:
            return (self.current_price - self.prev_closing_price) / self.prev_closing_price * 100
        return 0.0
    
    @property
    def is_limit_up(self) -> bool:
        """是否涨停"""
        if self.limit_up_price:
            return abs(self.current_price - self.limit_up_price) < 0.01
        return False
    
    @property
    def is_limit_down(self) -> bool:
        """是否跌停"""
        if self.limit_down_price:
            return abs(self.current_price - self.limit_down_price) < 0.01
        return False
    
    def update_price(self, new_price: float) -> None:
        """更新股价"""
        if new_price <= 0:
            return
        
        # 更新价格历史
        self.current_price = new_price
        self.highest_price = max(self.highest_price, new_price)
        self.lowest_price = min(self.lowest_price, new_price)
        self.last_trade_time = datetime.utcnow()
    
    def calculate_limit_prices(self) -> None:
        """计算涨跌停价格"""
        if self.prev_closing_price:
            base_price = self.prev_closing_price
            limit_percent = 0.20 if self.is_st else 0.10  # ST股票20%，普通股票10%
            
            self.limit_up_price = round(base_price * (1 + limit_percent), 2)
            self.limit_down_price = round(base_price * (1 - limit_percent), 2)
    
    def update_technical_indicators(self, price_history: List[float]) -> None:
        """更新技术指标"""
        if len(price_history) >= 5:
            self.ma5 = sum(price_history[-5:]) / 5
        if len(price_history) >= 10:
            self.ma10 = sum(price_history[-10:]) / 10
        if len(price_history) >= 20:
            self.ma20 = sum(price_history[-20:]) / 20
        if len(price_history) >= 60:
            self.ma60 = sum(price_history[-60:]) / 60
    
    def is_tradeable(self) -> bool:
        """是否可交易"""
        return self.is_active and not self.is_suspended
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "code": self.code,
            "name": self.name,
            "industry": self.industry,
            "current_price": self.current_price,
            "opening_price": self.opening_price,
            "closing_price": self.closing_price,
            "highest_price": self.highest_price,
            "lowest_price": self.lowest_price,
            "prev_closing_price": self.prev_closing_price,
            "change_amount": self.change_amount,
            "change_percent": self.change_percent,
            "volume": self.volume,
            "turnover": self.turnover,
            "market_cap": self.market_cap,
            "pe_ratio": self.pe_ratio,
            "pb_ratio": self.pb_ratio,
            "ma5": self.ma5,
            "ma10": self.ma10,
            "ma20": self.ma20,
            "ma60": self.ma60,
            "volatility": self.volatility,
            "beta": self.beta,
            "sentiment_score": self.sentiment_score,
            "is_active": self.is_active,
            "is_suspended": self.is_suspended,
            "is_st": self.is_st,
            "is_limit_up": self.is_limit_up,
            "is_limit_down": self.is_limit_down,
            "limit_up_price": self.limit_up_price,
            "limit_down_price": self.limit_down_price,
            "last_trade_time": self.last_trade_time.isoformat() if self.last_trade_time else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
