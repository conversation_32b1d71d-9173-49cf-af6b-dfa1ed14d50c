import { LucideIcon } from "lucide-react";
import React from "react";
import { Button } from '..';
export type ActionDialogProps = {
    trigger?: React.ReactNode;
    open?: boolean;
    onClose?: () => void;
    onOpenChange?: (open: boolean) => void;
    titleIcon?: LucideIcon;
    title: boolean | React.ReactNode;
    description?: React.ReactNode;
    danger?: boolean;
    okButton?: boolean | Readonly<{
        label?: string;
        onClick?: () => Promise<"prevent-close" | undefined | void>;
        props?: Partial<React.ComponentProps<typeof Button>>;
    }>;
    cancelButton?: boolean | Readonly<{
        label?: string;
        onClick?: () => Promise<"prevent-close" | undefined | void>;
        props?: Partial<React.ComponentProps<typeof Button>>;
    }>;
    confirmText?: string;
    children?: React.ReactNode;
    preventClose?: boolean;
};
export declare function ActionDialog(props: ActionDialogProps): import("react/jsx-runtime").JSX.Element;
