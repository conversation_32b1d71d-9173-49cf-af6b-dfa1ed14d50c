import { Column } from "@tanstack/react-table";
type DataTableColumnHeaderProps<TData, TValue> = {
    column: Column<TData, TValue>;
    columnTitle: React.ReactNode;
} & React.HTMLAttributes<HTMLDivElement>;
export declare function DataTableColumnHeader<TData, TValue>({ column, columnTitle, className, }: DataTableColumnHeaderProps<TData, TValue>): import("react/jsx-runtime").JSX.Element;
export {};
