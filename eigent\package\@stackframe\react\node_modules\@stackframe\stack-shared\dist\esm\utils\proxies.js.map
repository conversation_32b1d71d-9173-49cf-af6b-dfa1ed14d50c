{"version": 3, "sources": ["../../../src/utils/proxies.tsx"], "sourcesContent": ["import { nicify } from \"./strings\";\n\nexport function logged<T extends object>(name: string, toLog: T, options: {} = {}): T {\n  const proxy = new Proxy(toLog, {\n    get(target, prop, receiver) {\n      const orig = Reflect.get(target, prop, receiver);\n      if (typeof orig === \"function\") {\n        return function (this: any, ...args: any[]) {\n          const success = (v: any, isPromise: boolean) => console.debug(`logged(...): Called ${name}.${String(prop)}(${args.map(a => nicify(a)).join(\", \")}) => ${isPromise ? \"Promise<\" : \"\"}${nicify(result)}${isPromise ? \">\" : \"\"}`, { this: this, args, promise: isPromise ? result : false, result: v, trace: new Error() });\n          const error = (e: any, isPromise: boolean) => console.debug(`logged(...): Error in ${name}.${String(prop)}(${args.map(a => nicify(a)).join(\", \")})`, { this: this, args, promise: isPromise ? result : false, error: e, trace: new Error() });\n\n          let result: unknown;\n          try {\n            result = orig.apply(this, args);\n          } catch (e) {\n            error(e, false);\n            throw e;\n          }\n          if (result instanceof Promise) {\n            result.then((v) => success(v, true)).catch((e) => error(e, true));\n          } else {\n            success(result, false);\n          }\n          return result;\n        };\n      }\n      return orig;\n    },\n    set(target, prop, value) {\n      console.log(`Setting ${name}.${String(prop)} to ${value}`);\n      return Reflect.set(target, prop, value);\n    },\n    apply(target, thisArg, args) {\n      console.log(`Calling ${name}(${JSON.stringify(args).slice(1, -1)})`);\n      return Reflect.apply(target as any, thisArg, args);\n    },\n    construct(target, args, newTarget) {\n      console.log(`Constructing ${name}(${JSON.stringify(args).slice(1, -1)})`);\n      return Reflect.construct(target as any, args, newTarget);\n    },\n    defineProperty(target, prop, descriptor) {\n      console.log(`Defining ${name}.${String(prop)} as ${JSON.stringify(descriptor)}`);\n      return Reflect.defineProperty(target, prop, descriptor);\n    },\n    deleteProperty(target, prop) {\n      console.log(`Deleting ${name}.${String(prop)}`);\n      return Reflect.deleteProperty(target, prop);\n    },\n    setPrototypeOf(target, prototype) {\n      console.log(`Setting prototype of ${name} to ${prototype}`);\n      return Reflect.setPrototypeOf(target, prototype);\n    },\n    preventExtensions(target) {\n      console.log(`Preventing extensions of ${name}`);\n      return Reflect.preventExtensions(target);\n    },\n  });\n  return proxy;\n}\n\nexport function createLazyProxy<FactoryResult>(factory: () => FactoryResult): FactoryResult {\n  let cache: FactoryResult | undefined = undefined;\n  let initialized: boolean = false;\n\n  function initializeIfNeeded() {\n    if (!initialized) {\n      cache = factory();\n      initialized = true;\n    }\n    return cache!;\n  }\n\n  return new Proxy({}, {\n    get(target, prop, receiver) {\n      const instance = initializeIfNeeded();\n      return Reflect.get(instance, prop, receiver);\n    },\n    set(target, prop, value, receiver) {\n      const instance = initializeIfNeeded();\n      return Reflect.set(instance, prop, value, receiver);\n    },\n    has(target, prop) {\n      const instance = initializeIfNeeded();\n      return Reflect.has(instance, prop);\n    },\n    deleteProperty(target, prop) {\n      const instance = initializeIfNeeded();\n      return Reflect.deleteProperty(instance, prop);\n    },\n    ownKeys(target) {\n      const instance = initializeIfNeeded();\n      return Reflect.ownKeys(instance);\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      const instance = initializeIfNeeded();\n      return Reflect.getOwnPropertyDescriptor(instance, prop);\n    },\n    defineProperty(target, prop, descriptor) {\n      const instance = initializeIfNeeded();\n      return Reflect.defineProperty(instance, prop, descriptor);\n    },\n    getPrototypeOf(target) {\n      const instance = initializeIfNeeded();\n      return Reflect.getPrototypeOf(instance);\n    },\n    setPrototypeOf(target, proto) {\n      const instance = initializeIfNeeded();\n      return Reflect.setPrototypeOf(instance, proto);\n    },\n    isExtensible(target) {\n      const instance = initializeIfNeeded();\n      return Reflect.isExtensible(instance);\n    },\n    preventExtensions(target) {\n      const instance = initializeIfNeeded();\n      return Reflect.preventExtensions(instance);\n    },\n    apply(target, thisArg, argumentsList) {\n      const instance = initializeIfNeeded();\n      return Reflect.apply(instance as any, thisArg, argumentsList);\n    },\n    construct(target, argumentsList, newTarget) {\n      const instance = initializeIfNeeded();\n      return Reflect.construct(instance as any, argumentsList, newTarget);\n    }\n  }) as FactoryResult;\n}\nundefined?.test(\"createLazyProxy\", ({ expect }) => {\n  // Test with a simple object factory\n  let factoryCallCount = 0;\n  const createObject = () => {\n    factoryCallCount++;\n    return { value: 42, method: () => \"hello\" };\n  };\n\n  const proxy = createLazyProxy(createObject);\n\n  // Factory should not be called until property is accessed\n  expect(factoryCallCount).toBe(0);\n\n  // Accessing a property should initialize the object\n  expect(proxy.value).toBe(42);\n  expect(factoryCallCount).toBe(1);\n\n  // Accessing another property should not call factory again\n  expect(proxy.method()).toBe(\"hello\");\n  expect(factoryCallCount).toBe(1);\n\n  // Test with property setting\n  proxy.value = 100;\n  expect(proxy.value).toBe(100);\n  expect(factoryCallCount).toBe(1);\n\n  // Test with a class factory\n  let classFactoryCallCount = 0;\n  class TestClass {\n    constructor() {\n      classFactoryCallCount++;\n    }\n\n    getValue() {\n      return \"class value\";\n    }\n  }\n\n  const classFactory = () => new TestClass();\n  const classProxy = createLazyProxy(classFactory);\n\n  // Factory should not be called until method is accessed\n  expect(classFactoryCallCount).toBe(0);\n\n  // Accessing a method should initialize the object\n  expect(classProxy.getValue()).toBe(\"class value\");\n  expect(classFactoryCallCount).toBe(1);\n\n  // Accessing the method again should not call factory again\n  expect(classProxy.getValue()).toBe(\"class value\");\n  expect(classFactoryCallCount).toBe(1);\n});\n"], "mappings": ";AAAA,SAAS,cAAc;AAEhB,SAAS,OAAyB,MAAc,OAAU,UAAc,CAAC,GAAM;AACpF,QAAM,QAAQ,IAAI,MAAM,OAAO;AAAA,IAC7B,IAAI,QAAQ,MAAM,UAAU;AAC1B,YAAM,OAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;AAC/C,UAAI,OAAO,SAAS,YAAY;AAC9B,eAAO,YAAwB,MAAa;AAC1C,gBAAM,UAAU,CAAC,GAAQ,cAAuB,QAAQ,MAAM,uBAAuB,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAK,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,YAAY,aAAa,EAAE,GAAG,OAAO,MAAM,CAAC,GAAG,YAAY,MAAM,EAAE,IAAI,EAAE,MAAM,MAAM,MAAM,SAAS,YAAY,SAAS,OAAO,QAAQ,GAAG,OAAO,IAAI,MAAM,EAAE,CAAC;AACvT,gBAAM,QAAQ,CAAC,GAAQ,cAAuB,QAAQ,MAAM,yBAAyB,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAK,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,EAAE,MAAM,MAAM,MAAM,SAAS,YAAY,SAAS,OAAO,OAAO,GAAG,OAAO,IAAI,MAAM,EAAE,CAAC;AAE5O,cAAI;AACJ,cAAI;AACF,qBAAS,KAAK,MAAM,MAAM,IAAI;AAAA,UAChC,SAAS,GAAG;AACV,kBAAM,GAAG,KAAK;AACd,kBAAM;AAAA,UACR;AACA,cAAI,kBAAkB,SAAS;AAC7B,mBAAO,KAAK,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC;AAAA,UAClE,OAAO;AACL,oBAAQ,QAAQ,KAAK;AAAA,UACvB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,IAAI,QAAQ,MAAM,OAAO;AACvB,cAAQ,IAAI,WAAW,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,EAAE;AACzD,aAAO,QAAQ,IAAI,QAAQ,MAAM,KAAK;AAAA,IACxC;AAAA,IACA,MAAM,QAAQ,SAAS,MAAM;AAC3B,cAAQ,IAAI,WAAW,IAAI,IAAI,KAAK,UAAU,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG;AACnE,aAAO,QAAQ,MAAM,QAAe,SAAS,IAAI;AAAA,IACnD;AAAA,IACA,UAAU,QAAQ,MAAM,WAAW;AACjC,cAAQ,IAAI,gBAAgB,IAAI,IAAI,KAAK,UAAU,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG;AACxE,aAAO,QAAQ,UAAU,QAAe,MAAM,SAAS;AAAA,IACzD;AAAA,IACA,eAAe,QAAQ,MAAM,YAAY;AACvC,cAAQ,IAAI,YAAY,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,UAAU,CAAC,EAAE;AAC/E,aAAO,QAAQ,eAAe,QAAQ,MAAM,UAAU;AAAA,IACxD;AAAA,IACA,eAAe,QAAQ,MAAM;AAC3B,cAAQ,IAAI,YAAY,IAAI,IAAI,OAAO,IAAI,CAAC,EAAE;AAC9C,aAAO,QAAQ,eAAe,QAAQ,IAAI;AAAA,IAC5C;AAAA,IACA,eAAe,QAAQ,WAAW;AAChC,cAAQ,IAAI,wBAAwB,IAAI,OAAO,SAAS,EAAE;AAC1D,aAAO,QAAQ,eAAe,QAAQ,SAAS;AAAA,IACjD;AAAA,IACA,kBAAkB,QAAQ;AACxB,cAAQ,IAAI,4BAA4B,IAAI,EAAE;AAC9C,aAAO,QAAQ,kBAAkB,MAAM;AAAA,IACzC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEO,SAAS,gBAA+B,SAA6C;AAC1F,MAAI,QAAmC;AACvC,MAAI,cAAuB;AAE3B,WAAS,qBAAqB;AAC5B,QAAI,CAAC,aAAa;AAChB,cAAQ,QAAQ;AAChB,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,MAAM,CAAC,GAAG;AAAA,IACnB,IAAI,QAAQ,MAAM,UAAU;AAC1B,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,IAAI,UAAU,MAAM,QAAQ;AAAA,IAC7C;AAAA,IACA,IAAI,QAAQ,MAAM,OAAO,UAAU;AACjC,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,IAAI,UAAU,MAAM,OAAO,QAAQ;AAAA,IACpD;AAAA,IACA,IAAI,QAAQ,MAAM;AAChB,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,IAAI,UAAU,IAAI;AAAA,IACnC;AAAA,IACA,eAAe,QAAQ,MAAM;AAC3B,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,eAAe,UAAU,IAAI;AAAA,IAC9C;AAAA,IACA,QAAQ,QAAQ;AACd,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC;AAAA,IACA,yBAAyB,QAAQ,MAAM;AACrC,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,yBAAyB,UAAU,IAAI;AAAA,IACxD;AAAA,IACA,eAAe,QAAQ,MAAM,YAAY;AACvC,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,eAAe,UAAU,MAAM,UAAU;AAAA,IAC1D;AAAA,IACA,eAAe,QAAQ;AACrB,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,eAAe,QAAQ;AAAA,IACxC;AAAA,IACA,eAAe,QAAQ,OAAO;AAC5B,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,eAAe,UAAU,KAAK;AAAA,IAC/C;AAAA,IACA,aAAa,QAAQ;AACnB,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,aAAa,QAAQ;AAAA,IACtC;AAAA,IACA,kBAAkB,QAAQ;AACxB,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,kBAAkB,QAAQ;AAAA,IAC3C;AAAA,IACA,MAAM,QAAQ,SAAS,eAAe;AACpC,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,MAAM,UAAiB,SAAS,aAAa;AAAA,IAC9D;AAAA,IACA,UAAU,QAAQ,eAAe,WAAW;AAC1C,YAAM,WAAW,mBAAmB;AACpC,aAAO,QAAQ,UAAU,UAAiB,eAAe,SAAS;AAAA,IACpE;AAAA,EACF,CAAC;AACH;", "names": []}