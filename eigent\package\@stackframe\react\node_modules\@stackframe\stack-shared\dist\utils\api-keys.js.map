{"version": 3, "sources": ["../../src/utils/api-keys.tsx"], "sourcesContent": ["import crc32 from 'crc/crc32';\nimport { getBase32CharacterFromIndex } from \"./bytes\";\nimport { generateSecureRandomString } from \"./crypto\";\nimport { StackAssertionError } from \"./errors\";\n\n\nconst STACK_AUTH_MARKER = \"574ck4u7h\";\n\n// API key part lengths\nconst API_KEY_LENGTHS = {\n  SECRET_PART: 45,\n  ID_PART: 32,\n  TYPE_PART: 4,\n  SCANNER: 1,\n  MARKER: 9,\n  CHECKSUM: 8,\n} as const;\n\n/**\n * An api key has the following format:\n * <prefix_without_underscores>_<secret_part_45_chars><id_part_32_chars><type_user_or_team_4_chars><scanner_and_marker_10_chars><checksum_8_chars>\n *\n * The scanner and marker is a base32 character that is used to determine if the api key is a public or private key\n * and if it is a cloud or self-hosted key.\n *\n * The checksum is a crc32 checksum of the api key encoded in hex.\n *\n */\n\ntype ProjectApiKey = {\n  id: string,\n  prefix: string,\n  isPublic: boolean,\n  isCloudVersion: boolean,\n  secret: string,\n  checksum: string,\n  type: \"user\" | \"team\",\n}\n\n\nfunction createChecksumSync(checksummablePart: string): string {\n  const data = new TextEncoder().encode(checksummablePart);\n  const calculated_checksum = crc32(data);\n  return calculated_checksum.toString(16).padStart(8, \"0\");\n}\n\nfunction createApiKeyParts(options: Pick<ProjectApiKey, \"id\" | \"isPublic\" | \"isCloudVersion\" | \"type\">) {\n  const { id, isPublic, isCloudVersion, type } = options;\n  const prefix = isPublic ? \"pk\" : \"sk\";\n  const scannerFlag = (isCloudVersion ? 0 : 1) + (isPublic ? 2 : 0) + (/* version */ 0);\n\n  const secretPart = generateSecureRandomString();\n  const idPart = id.replace(/-/g, \"\");\n  const scannerAndMarker = getBase32CharacterFromIndex(scannerFlag).toLowerCase() + STACK_AUTH_MARKER;\n  const checksummablePart = `${prefix}_${secretPart}${idPart}${type}${scannerAndMarker}`;\n\n  return { checksummablePart, idPart, prefix, scannerAndMarker, type };\n}\n\n\nfunction parseApiKeyParts(secret: string) {\n  const regex = new RegExp(\n    `^([a-zA-Z0-9_]+)_` + // prefix\n    `([a-zA-Z0-9_]{${API_KEY_LENGTHS.SECRET_PART}})` + // secretPart\n    `([a-zA-Z0-9_]{${API_KEY_LENGTHS.ID_PART}})` + // idPart\n    `([a-zA-Z0-9_]{${API_KEY_LENGTHS.TYPE_PART}})` + // type\n    `([a-zA-Z0-9_]{${API_KEY_LENGTHS.SCANNER}})` + // scanner\n    `(${STACK_AUTH_MARKER})` + // marker\n    `([a-zA-Z0-9_]{${API_KEY_LENGTHS.CHECKSUM}})$` // checksum\n  );\n\n  const match = secret.match(regex);\n  if (!match) {\n    throw new StackAssertionError(\"Invalid API key format\");\n  }\n\n  const [, prefix, secretPart, idPart, type, scannerFlag, marker, checksum] = match;\n\n  const isCloudVersion = parseInt(scannerFlag, 32) % 2 === 0;\n  const isPublic = (parseInt(scannerFlag, 32) & 2) !== 0;\n\n  const checksummablePart = `${prefix}_${secretPart}${idPart}${type}${scannerFlag}${marker}`;\n  const restored_id = idPart.replace(/(.{8})(.{4})(.{4})(.{4})(.{12})/, \"$1-$2-$3-$4-$5\");\n\n  if (![\"user\", \"team\"].includes(type)) {\n    throw new StackAssertionError(\"Invalid type\");\n  }\n\n  return { checksummablePart, checksum, id: restored_id, isCloudVersion, isPublic, prefix, type: type as \"user\" | \"team\" };\n}\n\n\nexport function isApiKey(secret: string): boolean {\n  return secret.includes(\"_\") && secret.includes(STACK_AUTH_MARKER);\n}\n\nexport function createProjectApiKey(options: Pick<ProjectApiKey, \"id\" | \"isPublic\" | \"isCloudVersion\" | \"type\">): string {\n  const { checksummablePart } = createApiKeyParts(options);\n  const checksum = createChecksumSync(checksummablePart);\n  return `${checksummablePart}${checksum}`;\n}\n\n\nexport function parseProjectApiKey(secret: string): ProjectApiKey {\n  const { checksummablePart, checksum, id, isCloudVersion, isPublic, prefix, type } = parseApiKeyParts(secret);\n  const calculated_checksum = createChecksumSync(checksummablePart);\n\n  if (calculated_checksum !== checksum) {\n    throw new StackAssertionError(\"Checksum mismatch\");\n  }\n\n  return {\n    id,\n    prefix,\n    isPublic,\n    isCloudVersion,\n    secret,\n    checksum,\n    type,\n  };\n}\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAClB,mBAA4C;AAC5C,oBAA2C;AAC3C,oBAAoC;AAGpC,IAAM,oBAAoB;AAG1B,IAAM,kBAAkB;AAAA,EACtB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AACZ;AAwBA,SAAS,mBAAmB,mBAAmC;AAC7D,QAAM,OAAO,IAAI,YAAY,EAAE,OAAO,iBAAiB;AACvD,QAAM,0BAAsB,aAAAA,SAAM,IAAI;AACtC,SAAO,oBAAoB,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACzD;AAEA,SAAS,kBAAkB,SAA6E;AACtG,QAAM,EAAE,IAAI,UAAU,gBAAgB,KAAK,IAAI;AAC/C,QAAM,SAAS,WAAW,OAAO;AACjC,QAAM,eAAe,iBAAiB,IAAI,MAAM,WAAW,IAAI;AAAA,EAAoB;AAEnF,QAAM,iBAAa,0CAA2B;AAC9C,QAAM,SAAS,GAAG,QAAQ,MAAM,EAAE;AAClC,QAAM,uBAAmB,0CAA4B,WAAW,EAAE,YAAY,IAAI;AAClF,QAAM,oBAAoB,GAAG,MAAM,IAAI,UAAU,GAAG,MAAM,GAAG,IAAI,GAAG,gBAAgB;AAEpF,SAAO,EAAE,mBAAmB,QAAQ,QAAQ,kBAAkB,KAAK;AACrE;AAGA,SAAS,iBAAiB,QAAgB;AACxC,QAAM,QAAQ,IAAI;AAAA,IAChB,kCACiB,gBAAgB,WAAW,mBAC3B,gBAAgB,OAAO,mBACvB,gBAAgB,SAAS,mBACzB,gBAAgB,OAAO,MACpC,iBAAiB,kBACJ,gBAAgB,QAAQ;AAAA;AAAA,EAC3C;AAEA,QAAM,QAAQ,OAAO,MAAM,KAAK;AAChC,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,kCAAoB,wBAAwB;AAAA,EACxD;AAEA,QAAM,CAAC,EAAE,QAAQ,YAAY,QAAQ,MAAM,aAAa,QAAQ,QAAQ,IAAI;AAE5E,QAAM,iBAAiB,SAAS,aAAa,EAAE,IAAI,MAAM;AACzD,QAAM,YAAY,SAAS,aAAa,EAAE,IAAI,OAAO;AAErD,QAAM,oBAAoB,GAAG,MAAM,IAAI,UAAU,GAAG,MAAM,GAAG,IAAI,GAAG,WAAW,GAAG,MAAM;AACxF,QAAM,cAAc,OAAO,QAAQ,mCAAmC,gBAAgB;AAEtF,MAAI,CAAC,CAAC,QAAQ,MAAM,EAAE,SAAS,IAAI,GAAG;AACpC,UAAM,IAAI,kCAAoB,cAAc;AAAA,EAC9C;AAEA,SAAO,EAAE,mBAAmB,UAAU,IAAI,aAAa,gBAAgB,UAAU,QAAQ,KAA8B;AACzH;AAGO,SAAS,SAAS,QAAyB;AAChD,SAAO,OAAO,SAAS,GAAG,KAAK,OAAO,SAAS,iBAAiB;AAClE;AAEO,SAAS,oBAAoB,SAAqF;AACvH,QAAM,EAAE,kBAAkB,IAAI,kBAAkB,OAAO;AACvD,QAAM,WAAW,mBAAmB,iBAAiB;AACrD,SAAO,GAAG,iBAAiB,GAAG,QAAQ;AACxC;AAGO,SAAS,mBAAmB,QAA+B;AAChE,QAAM,EAAE,mBAAmB,UAAU,IAAI,gBAAgB,UAAU,QAAQ,KAAK,IAAI,iBAAiB,MAAM;AAC3G,QAAM,sBAAsB,mBAAmB,iBAAiB;AAEhE,MAAI,wBAAwB,UAAU;AACpC,UAAM,IAAI,kCAAoB,mBAAmB;AAAA,EACnD;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["crc32"]}