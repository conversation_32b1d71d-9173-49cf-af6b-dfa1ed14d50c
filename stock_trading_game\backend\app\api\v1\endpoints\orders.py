"""
🐾 订单相关API端点
Order Related API Endpoints

提供订单创建、查询、取消等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_db
from app.models.order import Order, OrderType, OrderPriceType, OrderStatus
from app.models.user import User
from app.models.stock import Stock

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_orders(
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单列表
    """
    query = select(Order)
    
    # 用户筛选
    if user_id:
        query = query.where(Order.user_id == user_id)
    
    # 状态筛选
    if status:
        try:
            order_status = OrderStatus(status)
            query = query.where(Order.status == order_status)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的订单状态")
    
    # 分页和排序
    query = query.order_by(Order.created_at.desc()).offset(skip).limit(limit)
    
    result = await db.execute(query)
    orders = result.scalars().all()
    
    return [order.to_dict() for order in orders]


@router.get("/{order_id}", response_model=dict)
async def get_order(
    order_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取订单详情
    """
    result = await db.execute(
        select(Order).where(Order.id == order_id)
    )
    order = result.scalar_one_or_none()
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    return order.to_dict()


@router.post("/", response_model=dict)
async def create_order(
    user_id: int,
    stock_id: int,
    order_type: str,
    price_type: str,
    quantity: int,
    price: Optional[float] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    创建订单
    
    - **user_id**: 用户ID
    - **stock_id**: 股票ID
    - **order_type**: 订单类型 (buy/sell)
    - **price_type**: 价格类型 (market/limit)
    - **quantity**: 数量
    - **price**: 价格（限价单必填）
    """
    # 验证参数
    try:
        order_type_enum = OrderType(order_type)
        price_type_enum = OrderPriceType(price_type)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的订单类型或价格类型")
    
    if quantity <= 0:
        raise HTTPException(status_code=400, detail="数量必须大于0")
    
    if price_type_enum == OrderPriceType.LIMIT and (price is None or price <= 0):
        raise HTTPException(status_code=400, detail="限价单必须指定有效价格")
    
    # 验证用户存在
    result = await db.execute(
        select(User).where(and_(User.id == user_id, User.is_active == True))
    )
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 验证股票存在
    result = await db.execute(
        select(Stock).where(and_(Stock.id == stock_id, Stock.is_active == True))
    )
    stock = result.scalar_one_or_none()
    
    if not stock:
        raise HTTPException(status_code=404, detail="股票不存在")
    
    # 检查股票是否可交易
    if not stock.is_tradeable():
        raise HTTPException(status_code=400, detail="股票当前不可交易")
    
    # 生成订单编号
    import uuid
    import time
    timestamp = str(int(time.time()))
    unique_id = str(uuid.uuid4()).replace('-', '')[:8]
    order_no = f"O{timestamp}{unique_id}".upper()
    
    # 计算订单金额
    if price_type_enum == OrderPriceType.MARKET:
        # 市价单使用当前价格估算
        estimated_price = stock.current_price
    else:
        estimated_price = price
    
    total_amount = estimated_price * quantity
    
    # 检查资金或持仓
    if order_type_enum == OrderType.BUY:
        # 买入检查资金
        required_amount = total_amount * (1 + 0.001)  # 加上手续费
        if user.balance < required_amount:
            raise HTTPException(status_code=400, detail="资金不足")
    else:
        # 卖出检查持仓
        from app.models.position import Position
        result = await db.execute(
            select(Position).where(
                and_(Position.user_id == user_id, Position.stock_id == stock_id)
            )
        )
        position = result.scalar_one_or_none()
        
        if not position or position.available_quantity < quantity:
            raise HTTPException(status_code=400, detail="持仓不足")
    
    # 创建订单
    new_order = Order(
        order_no=order_no,
        user_id=user_id,
        stock_id=stock_id,
        order_type=order_type_enum,
        price_type=price_type_enum,
        price=price,
        quantity=quantity,
        total_amount=total_amount
    )
    
    db.add(new_order)
    await db.commit()
    await db.refresh(new_order)
    
    return {
        **new_order.to_dict(),
        "message": "订单创建成功"
    }


@router.post("/{order_id}/cancel", response_model=dict)
async def cancel_order(
    order_id: int,
    reason: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    取消订单
    
    - **order_id**: 订单ID
    - **reason**: 取消原因
    """
    result = await db.execute(
        select(Order).where(Order.id == order_id)
    )
    order = result.scalar_one_or_none()
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    if not order.can_cancel:
        raise HTTPException(status_code=400, detail="订单不能取消")
    
    # 取消订单
    success = order.cancel(reason)
    
    if not success:
        raise HTTPException(status_code=400, detail="订单取消失败")
    
    await db.commit()
    
    return {
        **order.to_dict(),
        "message": "订单取消成功"
    }


@router.get("/user/{user_id}/active", response_model=List[dict])
async def get_user_active_orders(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户活跃订单
    """
    result = await db.execute(
        select(Order).where(
            and_(
                Order.user_id == user_id,
                Order.status.in_([OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED])
            )
        ).order_by(Order.created_at.desc())
    )
    orders = result.scalars().all()
    
    return [order.to_dict() for order in orders]
