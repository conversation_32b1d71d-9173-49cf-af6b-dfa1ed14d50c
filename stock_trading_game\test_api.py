#!/usr/bin/env python3
"""
🐾 API测试脚本
API Test Script

测试模拟炒股游戏的API功能
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

async def test_api():
    """测试API功能"""
    async with aiohttp.ClientSession() as session:
        print("🐾 开始测试模拟炒股游戏API...")
        
        # 1. 测试健康检查
        print("\n1. 测试健康检查...")
        await test_health_check(session)
        
        # 2. 测试股票API
        print("\n2. 测试股票API...")
        await test_stocks_api(session)
        
        # 3. 测试市场API
        print("\n3. 测试市场API...")
        await test_market_api(session)
        
        # 4. 测试用户API
        print("\n4. 测试用户API...")
        await test_users_api(session)
        
        print("\n✅ API测试完成！")

async def test_health_check(session: aiohttp.ClientSession):
    """测试健康检查"""
    try:
        async with session.get(f"{BASE_URL}/health") as resp:
            data = await resp.json()
            print(f"   健康检查: {data}")
            assert resp.status == 200
            print("   ✅ 健康检查通过")
    except Exception as e:
        print(f"   ❌ 健康检查失败: {e}")

async def test_stocks_api(session: aiohttp.ClientSession):
    """测试股票API"""
    try:
        # 获取股票列表
        async with session.get(f"{BASE_URL}/api/v1/stocks/") as resp:
            stocks = await resp.json()
            print(f"   获取到 {len(stocks)} 只股票")
            
            if stocks:
                stock = stocks[0]
                print(f"   第一只股票: {stock['code']} - {stock['name']}")
                
                # 测试获取单只股票
                stock_id = stock['id']
                async with session.get(f"{BASE_URL}/api/v1/stocks/{stock_id}") as resp2:
                    stock_detail = await resp2.json()
                    print(f"   股票详情: {stock_detail['name']} 当前价格: {stock_detail['current_price']}")
                
                # 测试实时数据
                async with session.get(f"{BASE_URL}/api/v1/stocks/{stock_id}/realtime") as resp3:
                    realtime_data = await resp3.json()
                    print(f"   实时数据: 市场情绪: {realtime_data.get('market_sentiment', 'N/A')}")
            
            print("   ✅ 股票API测试通过")
            
    except Exception as e:
        print(f"   ❌ 股票API测试失败: {e}")

async def test_market_api(session: aiohttp.ClientSession):
    """测试市场API"""
    try:
        # 测试市场状态
        async with session.get(f"{BASE_URL}/api/v1/market/status") as resp:
            status = await resp.json()
            print(f"   市场状态: {status}")
        
        # 测试市场概览
        async with session.get(f"{BASE_URL}/api/v1/market/overview") as resp:
            overview = await resp.json()
            print(f"   市场概览: 总股票数: {overview['total_stocks']}, 上涨: {overview['rising_stocks']}")
        
        # 测试涨幅榜
        async with session.get(f"{BASE_URL}/api/v1/market/gainers?limit=3") as resp:
            gainers = await resp.json()
            print(f"   涨幅榜前3: {[f\"{g['code']}-{g['change_percent']:.2f}%\" for g in gainers]}")
        
        print("   ✅ 市场API测试通过")
        
    except Exception as e:
        print(f"   ❌ 市场API测试失败: {e}")

async def test_users_api(session: aiohttp.ClientSession):
    """测试用户API"""
    try:
        # 创建测试用户
        user_data = {
            "username": f"testuser_{asyncio.get_event_loop().time():.0f}",
            "password": "123456",
            "email": f"test_{asyncio.get_event_loop().time():.0f}@example.com",
            "nickname": "测试猫咪"
        }
        
        async with session.post(f"{BASE_URL}/api/v1/users/", json=user_data) as resp:
            if resp.status == 200:
                user = await resp.json()
                print(f"   创建用户成功: {user['username']} (ID: {user['id']})")
                
                # 测试获取用户详情
                user_id = user['id']
                async with session.get(f"{BASE_URL}/api/v1/users/{user_id}") as resp2:
                    user_detail = await resp2.json()
                    print(f"   用户详情: 余额 {user_detail['balance']}")
                
                print("   ✅ 用户API测试通过")
            else:
                error = await resp.text()
                print(f"   ⚠️ 用户创建失败 (可能已存在): {error}")
        
    except Exception as e:
        print(f"   ❌ 用户API测试失败: {e}")

async def test_websocket():
    """测试WebSocket连接"""
    try:
        import websockets
        
        print("\n5. 测试WebSocket连接...")
        uri = "ws://localhost:8000/api/v1/market/ws"
        
        async with websockets.connect(uri) as websocket:
            print("   🔌 WebSocket连接成功")
            
            # 接收几条消息
            for i in range(3):
                message = await websocket.recv()
                data = json.loads(message)
                print(f"   📨 收到消息 {i+1}: 时间戳 {data.get('timestamp', 'N/A')}")
                
                if i < 2:  # 不是最后一条消息时等待
                    await asyncio.sleep(2)
            
            print("   ✅ WebSocket测试通过")
            
    except ImportError:
        print("   ⚠️ 跳过WebSocket测试 (需要安装websockets库)")
    except Exception as e:
        print(f"   ❌ WebSocket测试失败: {e}")

if __name__ == "__main__":
    print("🎮 模拟炒股游戏API测试")
    print("=" * 50)
    
    try:
        # 运行API测试
        asyncio.run(test_api())
        
        # 运行WebSocket测试
        asyncio.run(test_websocket())
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
