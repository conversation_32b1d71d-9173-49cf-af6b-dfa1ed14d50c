import React from "react";
import * as ToastPrimitives from "@radix-ui/react-toast";
import { type VariantProps } from "class-variance-authority";
declare const ToastProvider: React.FC<ToastPrimitives.ToastProviderProps>;
declare const ToastViewport: React.FC<Omit<ToastPrimitives.ToastViewportProps & React.RefAttributes<HTMLOListElement>, "ref"> & {
    ref?: React.Ref<HTMLOListElement> | undefined;
}>;
declare const Toast: React.FC<Omit<ToastPrimitives.ToastProps & React.RefAttributes<HTMLLIElement>, "ref"> & VariantProps<(props?: ({
    variant?: "default" | "destructive" | "success" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string> & {
    ref?: React.Ref<HTMLLIElement> | undefined;
}>;
declare const ToastAction: React.FC<Omit<ToastPrimitives.ToastActionProps & React.RefAttributes<HTMLButtonElement>, "ref"> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
}>;
declare const ToastClose: React.FC<Omit<ToastPrimitives.ToastCloseProps & React.RefAttributes<HTMLButtonElement>, "ref"> & {
    ref?: React.Ref<HTMLButtonElement> | undefined;
}>;
declare const ToastTitle: React.FC<Omit<ToastPrimitives.ToastTitleProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
declare const ToastDescription: React.FC<Omit<ToastPrimitives.ToastDescriptionProps & React.RefAttributes<HTMLDivElement>, "ref"> & {
    ref?: React.Ref<HTMLDivElement> | undefined;
}>;
type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;
type ToastActionElement = React.ReactElement<typeof ToastAction>;
export { type ToastProps, type ToastActionElement, ToastProvider, ToastViewport, Toast, ToastTitle, ToastDescription, ToastClose, ToastAction, };
