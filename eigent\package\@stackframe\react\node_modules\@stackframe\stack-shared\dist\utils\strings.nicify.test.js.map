{"version": 3, "sources": ["../../src/utils/strings.nicify.test.ts"], "sourcesContent": ["import { describe, expect, test } from \"vitest\";\nimport { NicifyOptions, deindent, nicify } from \"./strings\";\n\ndescribe(\"nicify\", () => {\n  describe(\"primitive values\", () => {\n    test(\"numbers\", () => {\n      expect(nicify(123)).toBe(\"123\");\n      expect(nicify(123n)).toBe(\"123n\");\n    });\n\n    test(\"strings\", () => {\n      expect(nicify(\"hello\")).toBe('\"hello\"');\n    });\n\n    test(\"booleans\", () => {\n      expect(nicify(true)).toBe(\"true\");\n      expect(nicify(false)).toBe(\"false\");\n    });\n\n    test(\"null and undefined\", () => {\n      expect(nicify(null)).toBe(\"null\");\n      expect(nicify(undefined)).toBe(\"undefined\");\n    });\n\n    test(\"symbols\", () => {\n      expect(nicify(Symbol(\"test\"))).toBe(\"Symbol(test)\");\n    });\n  });\n\n  describe(\"arrays\", () => {\n    test(\"empty array\", () => {\n      expect(nicify([])).toBe(\"[]\");\n    });\n\n    test(\"single-element array\", () => {\n      expect(nicify([1])).toBe(\"[1]\");\n    });\n\n    test(\"single-element array with long content\", () => {\n      expect(nicify([\"123123123123123\"])).toBe('[\"123123123123123\"]');\n    });\n\n    test(\"flat array\", () => {\n      expect(nicify([1, 2, 3])).toBe(\"[1, 2, 3]\");\n    });\n\n    test(\"longer array\", () => {\n      expect(nicify([10000, 2, 3])).toBe(deindent`\n        [\n          10000,\n          2,\n          3,\n        ]\n      `);\n    });\n\n    test(\"nested array\", () => {\n      expect(nicify([1, [2, 3]])).toBe(deindent`\n        [\n          1,\n          [2, 3],\n        ]\n      `);\n    });\n  });\n\n  describe(\"objects\", () => {\n    test(\"empty object\", () => {\n      expect(nicify({})).toBe(\"{}\");\n    });\n\n    test(\"simple object\", () => {\n      expect(nicify({ a: 1 })).toBe('{ \"a\": 1 }');\n    });\n\n    test(\"multiline object\", () => {\n      expect(nicify({ a: 1, b: 2 })).toBe(deindent`\n        {\n          \"a\": 1,\n          \"b\": 2,\n        }\n      `);\n    });\n  });\n\n  describe(\"custom classes\", () => {\n    test(\"class instance\", () => {\n      class TestClass {\n        constructor(public value: number) {}\n      }\n      expect(nicify(new TestClass(42))).toBe('TestClass { \"value\": 42 }');\n    });\n  });\n\n  describe(\"built-in objects\", () => {\n    test(\"URL\", () => {\n      expect(nicify(new URL(\"https://example.com\"))).toBe('URL(\"https://example.com/\")');\n    });\n\n    test(\"TypedArrays\", () => {\n      expect(nicify(new Uint8Array([1, 2, 3]))).toBe(\"Uint8Array([1,2,3])\");\n      expect(nicify(new Int32Array([1, 2, 3]))).toBe(\"Int32Array([1,2,3])\");\n    });\n\n    test(\"Error objects\", () => {\n      const error = new Error(\"test error\");\n      const nicifiedError = nicify({ error });\n      expect(nicifiedError).toMatch(new RegExp(deindent`\n        ^\\{\n          \"error\": Error: test error\n            Stack:\n              at (.|\\\\n)*\n        \\}$\n      `));\n    });\n\n    test(\"Error objects with cause and an extra property\", () => {\n      const error = new Error(\"test error\", { cause: new Error(\"cause\") });\n      (error as any).extra = \"something\";\n      const nicifiedError = nicify(error, { lineIndent: \"--\" });\n      expect(nicifiedError).toMatch(new RegExp(deindent`\n        ^Error: test error\n        --Stack:\n        ----at (.|\\\\n)+\n        --Extra properties: \\{ \"extra\": \"something\" \\}\n        --Cause:\n        ----Error: cause\n        ------Stack:\n        --------at (.|\\\\n)+$\n      `));\n    });\n\n    test(\"Headers\", () => {\n      const headers = new Headers();\n      headers.append(\"Content-Type\", \"application/json\");\n      headers.append(\"Accept\", \"text/plain\");\n      expect(nicify(headers)).toBe(deindent`\n        Headers {\n          \"accept\": \"text/plain\",\n          \"content-type\": \"application/json\",\n        }`\n      );\n    });\n  });\n\n  describe(\"multiline strings\", () => {\n    test(\"basic multiline\", () => {\n      expect(nicify(\"line1\\nline2\")).toBe('deindent`\\n  line1\\n  line2\\n`');\n    });\n\n    test(\"multiline with trailing newline\", () => {\n      expect(nicify(\"line1\\nline2\\n\")).toBe('deindent`\\n  line1\\n  line2\\n` + \"\\\\n\"');\n    });\n  });\n\n  describe(\"circular references\", () => {\n    test(\"object with self reference\", () => {\n      const circular: any = { a: 1 };\n      circular.self = circular;\n      expect(nicify(circular)).toBe(deindent`\n        {\n          \"a\": 1,\n          \"self\": Ref<value>,\n        }`\n      );\n    });\n  });\n\n  describe(\"configuration options\", () => {\n    test(\"maxDepth\", () => {\n      const deep = { a: { b: { c: { d: { e: 1 } } } } };\n      expect(nicify(deep, { maxDepth: 2 })).toBe('{ \"a\": { \"b\": { ... } } }');\n    });\n\n    test(\"lineIndent\", () => {\n      expect(nicify({ a: 1, b: 2 }, { lineIndent: \"    \" })).toBe(deindent`\n        {\n            \"a\": 1,\n            \"b\": 2,\n        }\n      `);\n    });\n\n    test(\"hideFields\", () => {\n      expect(nicify({ a: 1, b: 2, secret: \"hidden\" }, { hideFields: [\"secret\"] })).toBe(deindent`\n        {\n          \"a\": 1,\n          \"b\": 2,\n          <some fields may have been hidden>,\n        }\n      `);\n    });\n  });\n\n  describe(\"custom overrides\", () => {\n    test(\"override with custom type\", () => {\n      expect(nicify({ type: \"special\" }, {\n        overrides: ((value: unknown) => {\n          if (typeof value === \"object\" && value && \"type\" in value && (value as any).type === \"special\") {\n            return \"SPECIAL\";\n          }\n          return null;\n        }) as NicifyOptions[\"overrides\"]\n      })).toBe(\"SPECIAL\");\n    });\n  });\n\n  describe(\"functions\", () => {\n    test(\"named function\", () => {\n      expect(nicify(function namedFunction() {})).toBe(\"function namedFunction(...) { ... }\");\n    });\n\n    test(\"arrow function\", () => {\n      expect(nicify(() => {})).toBe(\"(...) => { ... }\");\n    });\n  });\n\n  describe(\"Nicifiable interface\", () => {\n    test(\"object implementing Nicifiable\", () => {\n      const nicifiable = {\n        value: 42,\n        getNicifiableKeys() {\n          return [\"value\"];\n        },\n        getNicifiedObjectExtraLines() {\n          return [\"// custom comment\"];\n        }\n      };\n      expect(nicify(nicifiable)).toBe(deindent`\n        {\n          \"value\": 42,\n          // custom comment,\n        }\n      `);\n    });\n  });\n\n  describe(\"unknown types\", () => {\n    test(\"object without prototype\", () => {\n      const unknownType = Object.create(null);\n      unknownType.value = \"test\";\n      expect(nicify(unknownType)).toBe('{ \"value\": \"test\" }');\n    });\n  });\n});\n"], "mappings": ";;;AAAA,oBAAuC;AACvC,qBAAgD;AAAA,IAEhD,wBAAS,UAAU,MAAM;AACvB,8BAAS,oBAAoB,MAAM;AACjC,4BAAK,WAAW,MAAM;AACpB,oCAAO,uBAAO,GAAG,CAAC,EAAE,KAAK,KAAK;AAC9B,oCAAO,uBAAO,IAAI,CAAC,EAAE,KAAK,MAAM;AAAA,IAClC,CAAC;AAED,4BAAK,WAAW,MAAM;AACpB,oCAAO,uBAAO,OAAO,CAAC,EAAE,KAAK,SAAS;AAAA,IACxC,CAAC;AAED,4BAAK,YAAY,MAAM;AACrB,oCAAO,uBAAO,IAAI,CAAC,EAAE,KAAK,MAAM;AAChC,oCAAO,uBAAO,KAAK,CAAC,EAAE,KAAK,OAAO;AAAA,IACpC,CAAC;AAED,4BAAK,sBAAsB,MAAM;AAC/B,oCAAO,uBAAO,IAAI,CAAC,EAAE,KAAK,MAAM;AAChC,oCAAO,uBAAO,MAAS,CAAC,EAAE,KAAK,WAAW;AAAA,IAC5C,CAAC;AAED,4BAAK,WAAW,MAAM;AACpB,oCAAO,uBAAO,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK,cAAc;AAAA,IACpD,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,UAAU,MAAM;AACvB,4BAAK,eAAe,MAAM;AACxB,oCAAO,uBAAO,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,IAC9B,CAAC;AAED,4BAAK,wBAAwB,MAAM;AACjC,oCAAO,uBAAO,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK;AAAA,IAChC,CAAC;AAED,4BAAK,0CAA0C,MAAM;AACnD,oCAAO,uBAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,qBAAqB;AAAA,IAChE,CAAC;AAED,4BAAK,cAAc,MAAM;AACvB,oCAAO,uBAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW;AAAA,IAC5C,CAAC;AAED,4BAAK,gBAAgB,MAAM;AACzB,oCAAO,uBAAO,CAAC,KAAO,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMlC;AAAA,IACH,CAAC;AAED,4BAAK,gBAAgB,MAAM;AACzB,oCAAO,uBAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAKhC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,WAAW,MAAM;AACxB,4BAAK,gBAAgB,MAAM;AACzB,oCAAO,uBAAO,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,IAC9B,CAAC;AAED,4BAAK,iBAAiB,MAAM;AAC1B,oCAAO,uBAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK,YAAY;AAAA,IAC5C,CAAC;AAED,4BAAK,oBAAoB,MAAM;AAC7B,oCAAO,uBAAO,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAKnC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,kBAAkB,MAAM;AAC/B,4BAAK,kBAAkB,MAAM;AAAA,MAC3B,MAAM,UAAU;AAAA,QACd,YAAmB,OAAe;AAAf;AAAA,QAAgB;AAAA,MACrC;AACA,oCAAO,uBAAO,IAAI,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,2BAA2B;AAAA,IACpE,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,oBAAoB,MAAM;AACjC,4BAAK,OAAO,MAAM;AAChB,oCAAO,uBAAO,IAAI,IAAI,qBAAqB,CAAC,CAAC,EAAE,KAAK,6BAA6B;AAAA,IACnF,CAAC;AAED,4BAAK,eAAe,MAAM;AACxB,oCAAO,uBAAO,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,qBAAqB;AACpE,oCAAO,uBAAO,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,qBAAqB;AAAA,IACtE,CAAC;AAED,4BAAK,iBAAiB,MAAM;AAC1B,YAAM,QAAQ,IAAI,MAAM,YAAY;AACpC,YAAM,oBAAgB,uBAAO,EAAE,MAAM,CAAC;AACtC,gCAAO,aAAa,EAAE,QAAQ,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMxC,CAAC;AAAA,IACJ,CAAC;AAED,4BAAK,kDAAkD,MAAM;AAC3D,YAAM,QAAQ,IAAI,MAAM,cAAc,EAAE,OAAO,IAAI,MAAM,OAAO,EAAE,CAAC;AACnE,MAAC,MAAc,QAAQ;AACvB,YAAM,oBAAgB,uBAAO,OAAO,EAAE,YAAY,KAAK,CAAC;AACxD,gCAAO,aAAa,EAAE,QAAQ,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OASxC,CAAC;AAAA,IACJ,CAAC;AAED,4BAAK,WAAW,MAAM;AACpB,YAAM,UAAU,IAAI,QAAQ;AAC5B,cAAQ,OAAO,gBAAgB,kBAAkB;AACjD,cAAQ,OAAO,UAAU,YAAY;AACrC,oCAAO,uBAAO,OAAO,CAAC,EAAE;AAAA,QAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,qBAAqB,MAAM;AAClC,4BAAK,mBAAmB,MAAM;AAC5B,oCAAO,uBAAO,cAAc,CAAC,EAAE,KAAK,gCAAgC;AAAA,IACtE,CAAC;AAED,4BAAK,mCAAmC,MAAM;AAC5C,oCAAO,uBAAO,gBAAgB,CAAC,EAAE,KAAK,wCAAwC;AAAA,IAChF,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,uBAAuB,MAAM;AACpC,4BAAK,8BAA8B,MAAM;AACvC,YAAM,WAAgB,EAAE,GAAG,EAAE;AAC7B,eAAS,OAAO;AAChB,oCAAO,uBAAO,QAAQ,CAAC,EAAE;AAAA,QAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAK9B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,yBAAyB,MAAM;AACtC,4BAAK,YAAY,MAAM;AACrB,YAAM,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAChD,oCAAO,uBAAO,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,2BAA2B;AAAA,IACxE,CAAC;AAED,4BAAK,cAAc,MAAM;AACvB,oCAAO,uBAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAK3D;AAAA,IACH,CAAC;AAED,4BAAK,cAAc,MAAM;AACvB,oCAAO,uBAAO,EAAE,GAAG,GAAG,GAAG,GAAG,QAAQ,SAAS,GAAG,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAMjF;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,oBAAoB,MAAM;AACjC,4BAAK,6BAA6B,MAAM;AACtC,oCAAO,uBAAO,EAAE,MAAM,UAAU,GAAG;AAAA,QACjC,WAAY,CAAC,UAAmB;AAC9B,cAAI,OAAO,UAAU,YAAY,SAAS,UAAU,SAAU,MAAc,SAAS,WAAW;AAC9F,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC,EAAE,KAAK,SAAS;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,aAAa,MAAM;AAC1B,4BAAK,kBAAkB,MAAM;AAC3B,oCAAO,uBAAO,SAAS,gBAAgB;AAAA,MAAC,CAAC,CAAC,EAAE,KAAK,qCAAqC;AAAA,IACxF,CAAC;AAED,4BAAK,kBAAkB,MAAM;AAC3B,oCAAO,uBAAO,MAAM;AAAA,MAAC,CAAC,CAAC,EAAE,KAAK,kBAAkB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,wBAAwB,MAAM;AACrC,4BAAK,kCAAkC,MAAM;AAC3C,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,oBAAoB;AAClB,iBAAO,CAAC,OAAO;AAAA,QACjB;AAAA,QACA,8BAA8B;AAC5B,iBAAO,CAAC,mBAAmB;AAAA,QAC7B;AAAA,MACF;AACA,oCAAO,uBAAO,UAAU,CAAC,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,OAK/B;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,8BAAS,iBAAiB,MAAM;AAC9B,4BAAK,4BAA4B,MAAM;AACrC,YAAM,cAAc,uBAAO,OAAO,IAAI;AACtC,kBAAY,QAAQ;AACpB,oCAAO,uBAAO,WAAW,CAAC,EAAE,KAAK,qBAAqB;AAAA,IACxD,CAAC;AAAA,EACH,CAAC;AACH,CAAC;", "names": []}