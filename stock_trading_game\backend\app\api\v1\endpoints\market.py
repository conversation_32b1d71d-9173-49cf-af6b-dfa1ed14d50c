"""
🐾 市场相关API端点
Market Related API Endpoints

提供市场数据、实时行情等功能
"""

from typing import List
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.responses import StreamingResponse
import json
import asyncio
from loguru import logger

from app.services.market_simulator import market_simulator

router = APIRouter()


@router.get("/status")
async def get_market_status():
    """
    获取市场状态
    """
    return {
        "is_market_open": market_simulator._is_market_open(),
        "market_sentiment": market_simulator.market_sentiment,
        "volatility_multiplier": market_simulator.volatility_multiplier,
        "total_stocks": len(market_simulator.stocks_cache),
        "simulator_running": market_simulator.is_running
    }


@router.get("/overview")
async def get_market_overview():
    """
    获取市场概览
    """
    stocks = list(market_simulator.stocks_cache.values())
    
    if not stocks:
        return {
            "total_stocks": 0,
            "rising_stocks": 0,
            "falling_stocks": 0,
            "unchanged_stocks": 0,
            "total_volume": 0,
            "total_turnover": 0.0,
            "market_sentiment": market_simulator.market_sentiment
        }
    
    # 统计涨跌股票数量
    rising_stocks = sum(1 for stock in stocks if stock.change_percent > 0)
    falling_stocks = sum(1 for stock in stocks if stock.change_percent < 0)
    unchanged_stocks = len(stocks) - rising_stocks - falling_stocks
    
    # 计算总成交量和成交额
    total_volume = sum(stock.volume for stock in stocks)
    total_turnover = sum(stock.volume * stock.current_price for stock in stocks)
    
    return {
        "total_stocks": len(stocks),
        "rising_stocks": rising_stocks,
        "falling_stocks": falling_stocks,
        "unchanged_stocks": unchanged_stocks,
        "total_volume": total_volume,
        "total_turnover": round(total_turnover, 2),
        "market_sentiment": market_simulator.market_sentiment,
        "is_market_open": market_simulator._is_market_open()
    }


@router.get("/hot-stocks")
async def get_hot_stocks(limit: int = 10):
    """
    获取热门股票（按成交量排序）
    
    - **limit**: 返回数量
    """
    stocks = list(market_simulator.stocks_cache.values())
    
    # 按成交量排序
    hot_stocks = sorted(stocks, key=lambda x: x.volume, reverse=True)[:limit]
    
    return [
        {
            "id": stock.id,
            "code": stock.code,
            "name": stock.name,
            "current_price": stock.current_price,
            "change_percent": stock.change_percent,
            "volume": stock.volume,
            "turnover": round(stock.volume * stock.current_price, 2)
        }
        for stock in hot_stocks
    ]


@router.get("/gainers")
async def get_gainers(limit: int = 10):
    """
    获取涨幅榜
    
    - **limit**: 返回数量
    """
    stocks = list(market_simulator.stocks_cache.values())
    
    # 按涨幅排序
    gainers = sorted(stocks, key=lambda x: x.change_percent, reverse=True)[:limit]
    
    return [
        {
            "id": stock.id,
            "code": stock.code,
            "name": stock.name,
            "current_price": stock.current_price,
            "change_amount": stock.change_amount,
            "change_percent": stock.change_percent,
            "volume": stock.volume
        }
        for stock in gainers
    ]


@router.get("/losers")
async def get_losers(limit: int = 10):
    """
    获取跌幅榜
    
    - **limit**: 返回数量
    """
    stocks = list(market_simulator.stocks_cache.values())
    
    # 按跌幅排序
    losers = sorted(stocks, key=lambda x: x.change_percent)[:limit]
    
    return [
        {
            "id": stock.id,
            "code": stock.code,
            "name": stock.name,
            "current_price": stock.current_price,
            "change_amount": stock.change_amount,
            "change_percent": stock.change_percent,
            "volume": stock.volume
        }
        for stock in losers
    ]


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket实时市场数据推送
    """
    await websocket.accept()
    logger.info("🔌 WebSocket连接已建立")
    
    try:
        # 发送实时数据流
        async for data in market_simulator.get_real_time_data():
            await websocket.send_text(json.dumps(data, ensure_ascii=False))
            
    except WebSocketDisconnect:
        logger.info("🔌 WebSocket连接已断开")
    except Exception as e:
        logger.error(f"❌ WebSocket错误: {e}")
        await websocket.close()


@router.get("/stream")
async def market_data_stream():
    """
    SSE (Server-Sent Events) 市场数据流
    """
    async def generate_data():
        """生成数据流"""
        async for data in market_simulator.get_real_time_data():
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate_data(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.post("/start-simulator")
async def start_market_simulator():
    """
    启动市场模拟器
    """
    if market_simulator.is_running:
        return {"message": "市场模拟器已在运行", "status": "running"}
    
    await market_simulator.start()
    return {"message": "市场模拟器启动成功", "status": "started"}


@router.post("/stop-simulator")
async def stop_market_simulator():
    """
    停止市场模拟器
    """
    if not market_simulator.is_running:
        return {"message": "市场模拟器未运行", "status": "stopped"}
    
    await market_simulator.stop()
    return {"message": "市场模拟器停止成功", "status": "stopped"}


@router.post("/trigger-market-event")
async def trigger_market_event(
    event_type: str,
    intensity: float = 0.1
):
    """
    触发市场事件（测试用）
    
    - **event_type**: 事件类型 (positive, negative, neutral)
    - **intensity**: 事件强度 (0.0-1.0)
    """
    if event_type == "positive":
        market_simulator.market_sentiment = min(1.0, market_simulator.market_sentiment + intensity)
    elif event_type == "negative":
        market_simulator.market_sentiment = max(-1.0, market_simulator.market_sentiment - intensity)
    else:  # neutral
        market_simulator.market_sentiment *= (1 - intensity)
    
    return {
        "message": f"已触发{event_type}市场事件",
        "new_sentiment": market_simulator.market_sentiment,
        "intensity": intensity
    }
