"""
🐾 市场数据模型
Market Data Model

定义股票历史数据和K线数据表结构
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Index, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class TimeFrame(str, Enum):
    """时间周期"""
    MINUTE_1 = "1m"      # 1分钟
    MINUTE_5 = "5m"      # 5分钟
    MINUTE_15 = "15m"    # 15分钟
    MINUTE_30 = "30m"    # 30分钟
    HOUR_1 = "1h"        # 1小时
    DAY_1 = "1d"         # 1天
    WEEK_1 = "1w"        # 1周
    MONTH_1 = "1M"       # 1月


class MarketData(Base):
    """市场数据模型（K线数据）"""
    
    __tablename__ = "market_data"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="数据ID")
    
    # 关联字段
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False, comment="股票ID")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), nullable=False, comment="时间戳")
    timeframe = Column(SQLEnum(TimeFrame), nullable=False, comment="时间周期")
    trade_date = Column(String(10), nullable=False, comment="交易日期 YYYY-MM-DD")
    
    # OHLCV数据
    open_price = Column(Float, nullable=False, comment="开盘价")
    high_price = Column(Float, nullable=False, comment="最高价")
    low_price = Column(Float, nullable=False, comment="最低价")
    close_price = Column(Float, nullable=False, comment="收盘价")
    volume = Column(Integer, default=0, comment="成交量")
    turnover = Column(Float, default=0.0, comment="成交额")
    
    # 技术指标
    ma5 = Column(Float, nullable=True, comment="5日均线")
    ma10 = Column(Float, nullable=True, comment="10日均线")
    ma20 = Column(Float, nullable=True, comment="20日均线")
    ma60 = Column(Float, nullable=True, comment="60日均线")
    
    # MACD指标
    macd = Column(Float, nullable=True, comment="MACD")
    macd_signal = Column(Float, nullable=True, comment="MACD信号线")
    macd_histogram = Column(Float, nullable=True, comment="MACD柱状图")
    
    # RSI指标
    rsi = Column(Float, nullable=True, comment="RSI相对强弱指标")
    
    # 布林带
    bb_upper = Column(Float, nullable=True, comment="布林带上轨")
    bb_middle = Column(Float, nullable=True, comment="布林带中轨")
    bb_lower = Column(Float, nullable=True, comment="布林带下轨")
    
    # KDJ指标
    kdj_k = Column(Float, nullable=True, comment="KDJ K值")
    kdj_d = Column(Float, nullable=True, comment="KDJ D值")
    kdj_j = Column(Float, nullable=True, comment="KDJ J值")
    
    # 其他指标
    atr = Column(Float, nullable=True, comment="平均真实波幅")
    obv = Column(Float, nullable=True, comment="能量潮指标")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关系定义
    stock = relationship("Stock", back_populates="market_data")
    
    # 索引
    __table_args__ = (
        Index('idx_market_data_stock_time', 'stock_id', 'timestamp'),
        Index('idx_market_data_stock_timeframe', 'stock_id', 'timeframe'),
        Index('idx_market_data_trade_date', 'trade_date'),
        Index('idx_market_data_timestamp', 'timestamp'),
        Index('idx_market_data_stock_date_frame', 'stock_id', 'trade_date', 'timeframe'),
    )
    
    def __repr__(self):
        return f"<MarketData(id={self.id}, stock_id={self.stock_id}, timestamp={self.timestamp}, timeframe={self.timeframe})>"
    
    @property
    def change_amount(self) -> float:
        """涨跌金额"""
        return self.close_price - self.open_price
    
    @property
    def change_percent(self) -> float:
        """涨跌幅百分比"""
        if self.open_price == 0:
            return 0.0
        return (self.change_amount / self.open_price) * 100
    
    @property
    def amplitude(self) -> float:
        """振幅"""
        if self.open_price == 0:
            return 0.0
        return ((self.high_price - self.low_price) / self.open_price) * 100
    
    @property
    def is_red_candle(self) -> bool:
        """是否为阳线"""
        return self.close_price > self.open_price
    
    @property
    def is_green_candle(self) -> bool:
        """是否为阴线"""
        return self.close_price < self.open_price
    
    @property
    def is_doji(self) -> bool:
        """是否为十字星"""
        return abs(self.close_price - self.open_price) < 0.01
    
    @property
    def upper_shadow(self) -> float:
        """上影线长度"""
        return self.high_price - max(self.open_price, self.close_price)
    
    @property
    def lower_shadow(self) -> float:
        """下影线长度"""
        return min(self.open_price, self.close_price) - self.low_price
    
    @property
    def body_size(self) -> float:
        """实体大小"""
        return abs(self.close_price - self.open_price)
    
    def calculate_vwap(self) -> float:
        """计算成交量加权平均价格"""
        if self.volume == 0:
            return (self.open_price + self.high_price + self.low_price + self.close_price) / 4
        
        # 简化计算，实际应该使用分时数据
        typical_price = (self.high_price + self.low_price + self.close_price) / 3
        return typical_price
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "stock_id": self.stock_id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "timeframe": self.timeframe.value,
            "trade_date": self.trade_date,
            "open_price": self.open_price,
            "high_price": self.high_price,
            "low_price": self.low_price,
            "close_price": self.close_price,
            "volume": self.volume,
            "turnover": self.turnover,
            "change_amount": self.change_amount,
            "change_percent": self.change_percent,
            "amplitude": self.amplitude,
            "is_red_candle": self.is_red_candle,
            "is_green_candle": self.is_green_candle,
            "is_doji": self.is_doji,
            "upper_shadow": self.upper_shadow,
            "lower_shadow": self.lower_shadow,
            "body_size": self.body_size,
            "ma5": self.ma5,
            "ma10": self.ma10,
            "ma20": self.ma20,
            "ma60": self.ma60,
            "macd": self.macd,
            "macd_signal": self.macd_signal,
            "macd_histogram": self.macd_histogram,
            "rsi": self.rsi,
            "bb_upper": self.bb_upper,
            "bb_middle": self.bb_middle,
            "bb_lower": self.bb_lower,
            "kdj_k": self.kdj_k,
            "kdj_d": self.kdj_d,
            "kdj_j": self.kdj_j,
            "atr": self.atr,
            "obv": self.obv,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
    
    def to_ohlcv(self) -> list:
        """转换为OHLCV格式"""
        return [
            self.timestamp.timestamp() * 1000 if self.timestamp else 0,  # 时间戳（毫秒）
            self.open_price,
            self.high_price,
            self.low_price,
            self.close_price,
            self.volume
        ]
